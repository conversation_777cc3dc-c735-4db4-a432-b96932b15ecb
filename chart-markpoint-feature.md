# 图表标记点功能实现文档

## 功能概述

在 MultiGridDrillChart.vue 组件中添加了卡钻和突进点的可视化标记功能，通过红色高亮和标记点来显示异常状态。

## 实现功能

### 1. 卡钻点标记
- **颜色**：红色 (#FF4D4F)
- **标记点**：图钉样式，显示"卡"字
- **高亮**：该数据点在所有曲线上都显示为红色

### 2. 上突进点标记
- **颜色**：红色 (#FF4D4F)
- **标记点**：图钉样式，显示向上箭头"↑"
- **高亮**：该数据点在所有曲线上都显示为红色

### 3. 下突进点标记
- **颜色**：红色 (#FF4D4F)
- **标记点**：图钉样式，显示向下箭头"↓"
- **高亮**：该数据点在所有曲线上都显示为红色

## 数据字段要求

组件期望接收的数据中包含以下标记字段：

```javascript
{
  // 原始数据字段...
  isStuckPoint: boolean,      // 是否为卡钻点
  isUpMutation: boolean,      // 是否为上突进点
  isDownMutation: boolean,    // 是否为下突进点
  // 其他字段...
}
```

## 实现细节

### 1. 数据索引收集
```javascript
// 收集卡钻和突进点的索引
const stuckPointIndices = new Set<number>()
const upMutationPointIndices = new Set<number>()
const downMutationPointIndices = new Set<number>()

// 遍历数据，找出异常点的索引
data.forEach((item: any, index: number) => {
  if (item.isStuckPoint) stuckPointIndices.add(index)
  if (item.isUpMutation) upMutationPointIndices.add(index)
  if (item.isDownMutation) downMutationPointIndices.add(index)
})
```

### 2. 标记点配置
```javascript
// 只在第一个网格添加标记点，避免重复显示
if (index === 0) {
  // 卡钻点标记
  markPointData.push({
    xAxis: pointIndex,
    yAxis: yValue,
    name: '卡钻点',
    value: '卡',
    itemStyle: { color: '#FF4D4F', borderColor: '#FFFFFF', borderWidth: 2 },
    label: { formatter: '卡', color: '#FFFFFF', fontWeight: 'bold', fontSize: 12 }
  })
  
  // 上突进点标记
  markPointData.push({
    // ... 配置向上箭头 ↑
  })
  
  // 下突进点标记
  markPointData.push({
    // ... 配置向下箭头 ↓
  })
}
```

### 3. 红色高亮系列
```javascript
// 为每个参数曲线添加红色高亮系列
const highlightData = paramData[config.field].map((value, dataIndex) => {
  if (stuckPointIndices.has(dataIndex) || 
      upMutationPointIndices.has(dataIndex) || 
      downMutationPointIndices.has(dataIndex)) {
    return value // 异常点显示原值
  }
  return null // 正常点不显示
})

series.push({
  name: `${config.name}_highlight`,
  type: 'line',
  data: highlightData,
  lineStyle: { color: '#FF4D4F', width: 3 },
  itemStyle: { color: '#FF4D4F', borderColor: '#FFFFFF', borderWidth: 2 },
  z: 5 // 确保在普通曲线之上
})
```

## 视觉效果

1. **正常状态**：绿色曲线正常显示
2. **卡钻状态**：
   - 该点及其连线变为红色
   - 顶部显示红色图钉标记，内容为"卡"
3. **上突进状态**：
   - 该点及其连线变为红色
   - 顶部显示红色图钉标记，内容为"↑"
4. **下突进状态**：
   - 该点及其连线变为红色
   - 顶部显示红色图钉标记，内容为"↓"

## 调试信息

组件会在控制台输出调试信息：
- 发现的卡钻点索引和数据
- 发现的上突进点索引和数据
- 发现的下突进点索引和数据
- 各类异常点的总数量

## 使用示例

```vue
<MultiGridDrillChart 
  :latest-data="latestDataWithMarkers" 
  :drilling-data="historicalDataWithMarkers" 
/>
```

其中数据应包含标记字段：
```javascript
const dataWithMarkers = {
  // 原始MQTT数据...
  isStuckPoint: false,
  isUpMutation: true,  // 这个点是上突进
  isDownMutation: false,
  mutationType: 'up'
}
```
