# 实时钻进数据算法优化文档

## 修改概述

针对 `generateDrillingRealTimeData` 函数进行了优化，以适应实时数据处理的需求。

## 主要改动

### 1. 输入数据处理
- **输入**：约5个连续数据点的数组
- **处理逻辑**：只计算最后一个点的岩石强度和围岩等级
- **输出**：只返回包含最后一个点的数组

### 2. 岩石强度计算
```javascript
// 只针对最后一个数据点计算
const lastItem = data[data.length - 1]
const advncSpd = Number(lastItem.advncSpd)
const rockStrengthInfo = getRockStrengthInfo(advncSpd)
```

### 3. 围岩等级计算
```javascript
// 基于最后一个点的钻进速度、推进力、旋转扭矩
const torqueDelta = Math.abs(Number(lastItem.rtnTq) - Number(lastItem.frcstPrs))
const standardTorque = Number(lastItem.rtnTq)
const rockClass = determineRockClass(advncSpd, torqueDelta, standardTorque)
```

### 4. 卡钻与突进检测
- **利用全部5个点**：进行连续数据分析
- **标记最后一个点**：检查最后一个点是否为卡钻或突进点
- **返回标记**：在结果中添加 `isStuckPoint` 和 `isMutationPoint` 字段

### 5. 预警等级计算
```javascript
// 地质预警：基于岩石强度和围岩等级
let geologicalWarningLevel = 0
if (rockStrengthLevel <= 15 || rockGradeLevel <= 30) {
  geologicalWarningLevel = 2 // 中风险
} else if (rockStrengthLevel <= 30 || rockGradeLevel <= 45) {
  geologicalWarningLevel = 1 // 低风险
}

// 设备预警：基于卡钻和突进情况
let equipmentWarningLevel = 0
if (isLastPointStuck) {
  equipmentWarningLevel = 3 // 高风险
} else if (isLastPointMutation) {
  equipmentWarningLevel = 2 // 中风险
}
```

## 输出数据结构

```javascript
[{
  // 原始数据字段
  ...lastItem,
  
  // 计算结果字段
  rockStrengthLevel: 60,        // 岩石强度评分(1-100)
  rockStrengthDesc: "坚固",      // 岩石强度描述
  rockGradeDesc: "Ⅲ类围岩",     // 围岩等级
  rockGradeLevel: 60,           // 围岩等级评分(1-100)
  geologicalWarning: 1,         // 地质预警等级(0-3)
  equipmentWarning: 0,          // 设备预警等级(0-3)
  isStuckPoint: false,          // 是否为卡钻点
  isMutationPoint: false        // 是否为突进点
}]
```

## 性能优化

1. **减少计算量**：只计算最后一个点的岩石属性
2. **保持检测精度**：利用全部5个点进行卡钻和突进检测
3. **简化输出**：只返回最后一个点，减少数据传输量
4. **实时响应**：适合高频实时数据处理场景

## 使用场景

- **实时监控**：MQTT数据流处理
- **增量更新**：图表组件实时更新
- **预警系统**：实时地质和设备预警
- **性能优化**：减少不必要的重复计算
