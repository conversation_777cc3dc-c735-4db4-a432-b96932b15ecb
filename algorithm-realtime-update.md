# 实时钻进数据算法优化文档

## 修改概述

针对 `generateDrillingRealTimeData` 函数进行了优化，以适应实时数据处理的需求。

## 主要改动

### 0. 数据获取策略
```javascript
// 从历史消息中获取前4个数据点，加上当前数据点组成数组
const historicalData = messages.value
  .slice(-4) // 获取最近的4个历史消息
  .map((msg: any) => snakeToCamel(msg.payload)) // 转换为驼峰格式

// 构建包含数据点的数组（历史数据 + 当前1个）
const dataPoints = [...historicalData, currentCamelData];
```

### 1. 输入数据处理
- **输入**：动态数量的连续数据点数组（1-5个）
- **数据来源**：历史消息 + 当前最新数据点
- **处理逻辑**：只计算最后一个点的岩石强度和围岩等级
- **输出**：只返回包含最后一个点的数组

### 2. 岩石强度计算
```javascript
// 只针对最后一个数据点计算
const lastItem = data[data.length - 1]
const advncSpd = Number(lastItem.advncSpd)
const rockStrengthInfo = getRockStrengthInfo(advncSpd)
```

### 3. 围岩等级计算
```javascript
// 基于最后一个点的钻进速度、推进力、旋转扭矩
const torqueDelta = Math.abs(Number(lastItem.rtnTq) - Number(lastItem.frcstPrs))
const standardTorque = Number(lastItem.rtnTq)
const rockClass = determineRockClass(advncSpd, torqueDelta, standardTorque)
```

### 4. 卡钻与突进检测
- **利用所有可用数据点**：进行连续数据分析（1-5个点）
- **标记最后一个点**：检查最后一个点是否为卡钻或突进点
- **区分突进类型**：分别检测上突进和下突进
- **返回标记**：在结果中添加详细的突进类型标记字段
- **动态适应**：数据点不足时仍能工作，但检测精度会降低

### 5. 预警等级计算
```javascript
// 地质预警：基于岩石强度和围岩等级
let geologicalWarningLevel = 0
if (rockStrengthLevel <= 15 || rockGradeLevel <= 30) {
  geologicalWarningLevel = 2 // 中风险
} else if (rockStrengthLevel <= 30 || rockGradeLevel <= 45) {
  geologicalWarningLevel = 1 // 低风险
}

// 设备预警：基于卡钻和突进情况
let equipmentWarningLevel = 0
if (isLastPointStuck) {
  equipmentWarningLevel = 3 // 高风险
} else if (isLastPointMutation) {
  equipmentWarningLevel = 2 // 中风险
}
```

## 输出数据结构

```javascript
[{
  // 原始数据字段
  ...lastItem,

  // 计算结果字段
  rockStrengthLevel: 60,        // 岩石强度评分(1-100)
  rockStrengthDesc: "坚固",      // 岩石强度描述
  rockGradeDesc: "Ⅲ类围岩",     // 围岩等级
  rockGradeLevel: 60,           // 围岩等级评分(1-100)
  geologicalWarning: 1,         // 地质预警等级(0-3)
  equipmentWarning: 0,          // 设备预警等级(0-3)
  isStuckPoint: false,          // 是否为卡钻点
  isMutationPoint: false,       // 是否为突进点（上突进或下突进）
  isUpMutation: false,          // 是否为上突进点
  isDownMutation: false,        // 是否为下突进点
  mutationType: null            // 突进类型：'up'、'down' 或 null
}]
```

### 突进类型说明

- **`isMutationPoint`**: 综合判断是否为突进点（上突进或下突进）
- **`isUpMutation`**: 专门标识是否为上突进点（钻进速度突然增加）
- **`isDownMutation`**: 专门标识是否为下突进点（钻进速度突然减少）
- **`mutationType`**: 突进类型字符串，便于快速判断：
  - `'up'`: 上突进
  - `'down'`: 下突进
  - `null`: 无突进

## 性能优化

1. **减少计算量**：只计算最后一个点的岩石属性
2. **动态检测精度**：利用所有可用数据点进行卡钻和突进检测
3. **简化输出**：只返回最后一个点，减少数据传输量
4. **实时响应**：适合高频实时数据处理场景
5. **渐进式增强**：随着历史数据积累，检测精度逐步提高

## 使用场景

- **实时监控**：MQTT数据流处理
- **增量更新**：图表组件实时更新
- **预警系统**：实时地质和设备预警
- **性能优化**：减少不必要的重复计算

## 使用示例

### 场景1：数据充足时（5个数据点）
```javascript
// 输入：5个连续数据点
const inputData = [
  { collectionAt: "2025-01-01T10:00:00Z", dpth: 100, advncSpd: 30, rtnTq: 150, frcstPrs: 140 },
  { collectionAt: "2025-01-01T10:00:01Z", dpth: 101, advncSpd: 32, rtnTq: 155, frcstPrs: 145 },
  { collectionAt: "2025-01-01T10:00:02Z", dpth: 102, advncSpd: 35, rtnTq: 160, frcstPrs: 150 },
  { collectionAt: "2025-01-01T10:00:03Z", dpth: 103, advncSpd: 45, rtnTq: 165, frcstPrs: 155 },
  { collectionAt: "2025-01-01T10:00:04Z", dpth: 104, advncSpd: 50, rtnTq: 170, frcstPrs: 160 }  // 最后一个点
]

// 调用算法 - 最佳检测精度
const result = generateDrillingRealTimeData(inputData)
```

### 场景2：数据不足时（1-4个数据点）
```javascript
// 输入：只有2个数据点（刚开始接收数据时）
const inputData = [
  { collectionAt: "2025-01-01T10:00:00Z", dpth: 100, advncSpd: 30, rtnTq: 150, frcstPrs: 140 },
  { collectionAt: "2025-01-01T10:00:01Z", dpth: 101, advncSpd: 32, rtnTq: 155, frcstPrs: 145 }  // 最后一个点
]

// 调用算法 - 降低的检测精度，但仍能计算岩石属性
const result = generateDrillingRealTimeData(inputData)
```

### 输出结果
```javascript
// 输出结果（只包含最后一个点）
console.log(result[0].mutationType) // 可能输出: 'up'、'down' 或 null
console.log(result[0].isUpMutation) // true 或 false
console.log(result[0].isDownMutation) // true 或 false
```
