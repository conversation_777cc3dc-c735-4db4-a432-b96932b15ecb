/**
 * 钻井参数 水压系数对照表
 * 根据高压水压力(bar)确定对应的水压系数(iwp)
 */
const waterPressure_P = {
  120: 0.8, // 0-120bar 对应系数0.8
  140: 0.9, // 121-140bar 对应系数0.9
  160: 1, // 141-160bar 对应系数1
  180: 1.1, // 161-180bar 对应系数1.1
  210: 1.2 // 181-210bar 对应系数1.2
}

/**
 * 转速系数对照表
 * 不同转速(rpm)对应的转速系数
 */
const rpmData = {
  70: 1, // 70rpm 对应系数1
  80: 1, // 80rpm 对应系数1
  90: 1, // 90rpm 对应系数1
  100: 1, // 100rpm 对应系数1
  110: 1, // 110rpm 对应系数1
  120: 1 // 120rpm 对应系数1
}

/**
 * 钻头磨蚀系数数据
 * 根据深度记录不同转速下的磨蚀系数
 */
const depthData = [
  { depth: 0, rpmCoefficients: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] }, // 深度0时的系数
  { depth: 100, rpmCoefficients: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] }, // 深度100时的系数
  // ... 其他深度的数据
  { depth: 10000, rpmCoefficients: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1] } // 深度10000时的系数
]

/**
 * 扭矩参数配置
 * @param {number} depth - 深度(cm)
 * @param {number} Tp - 扭矩预设值(Nm)
 * @param {number} Tk - 卡钻扭矩阈值(Nm)
 * @param {number} ΔTc - 扭矩波动预设值(Nm)
 */
const torqueData = [
  { depth: 100, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 200, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 300, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 400, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 500, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 600, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 700, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 800, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 900, Tp: 120, Tk: 150, ΔTc: 20 },
  { depth: 10000, Tp: 120, Tk: 150, ΔTc: 20 }
]

const rockStrengthData = [
  { vi: 15, cs: '> 250', output: '极坚固' },
  { vi: 20, cs: '200–250', output: '极坚固' },
  { vi: 25, cs: '190–200', output: '极坚固' },
  { vi: 30, cs: '180–190', output: '很坚固' },
  { vi: 35, cs: '170–180', output: '很坚固' },
  { vi: 40, cs: '160–170', output: '很坚固' },
  { vi: 45, cs: '150–160', output: '很坚固' },
  { vi: 50, cs: '140–150', output: '很坚固' },
  { vi: 55, cs: '130–140', output: '坚固' },
  { vi: 60, cs: '120–130', output: '坚固' },
  { vi: 65, cs: '100–120', output: '坚固' },
  { vi: 70, cs: '80–100', output: '坚固' },
  { vi: 80, cs: '60–80', output: '比较坚固' },
  { vi: 90, cs: '50–60', output: '比较坚固' },
  { vi: 100, cs: '40–50', output: '中等坚固' },
  { vi: 120, cs: '30–40', output: '一般坚固' },
  { vi: 9999, cs: '< 30', output: '软岩' }
]

const rockClassificationData = [
  { speed: 20, deltaT: '< 0.7Tc', output: 'Ⅰ类围岩' },
  { speed: 20, deltaT: '< Tc', output: 'Ⅱ类围岩' },
  { speed: 20, deltaT: '> 1Tc', output: 'Ⅲ类围岩' },
  { speed: 40, deltaT: '< 0.6Tc', output: 'Ⅲ类围岩' },
  { speed: 40, deltaT: '< 0.8Tc', output: 'Ⅲ、Ⅳ类围岩' },
  { speed: 40, deltaT: '< 1.1Tc', output: 'Ⅳ类围岩' },
  { speed: 40, deltaT: '> 1.1Tc', output: 'Ⅳ、Ⅴ类围岩' },
  { speed: 80, deltaT: '< 0.6Tc', output: 'Ⅲ类围岩' },
  { speed: 80, deltaT: '< 1Tc', output: 'Ⅲ、Ⅳ类围岩' },
  { speed: 80, deltaT: '> 1Tc', output: 'Ⅳ、Ⅴ类围岩' },
  { speed: 150, deltaT: '< 0.8Tc', output: 'Ⅳ类围岩' },
  { speed: 150, deltaT: '< 0.9Tc', output: 'Ⅴ类围岩' },
  { speed: 150, deltaT: '> 1Tc', output: 'Ⅵ类围岩' },
  { speed: 9999, deltaT: '< 0.8Tc', output: 'Ⅴ类围岩' },
  { speed: 9999, deltaT: '< 1Tc', output: 'Ⅴ、Ⅵ类围岩' },
  { speed: 9999, deltaT: '> 1Tc', output: 'Ⅵ类围岩' }
]
/**
 * 卡钻风险评估表
 * @param {string} rockClass - 围岩等级
 * @param {string} Tc - 扭矩阈值比例条件
 * @param {string} riskLevel - 风险等级
 * @param {string} description - 风险描述
 */
const stuckRiskData = [
  {
    rockClass: 'Ⅰ、Ⅱ、Ⅲ',
    Tc: '<0.7Tk',
    riskLevel: '-',
    description: '无卡钻风险'
  },
  {
    rockClass: 'Ⅲ、Ⅳ',
    Tc: '>0.9Tk',
    riskLevel: 'SL-A',
    description: 'SL-A：卡钻风险高，旋转扭矩接近防卡钻保护阈值'
  },
  {
    rockClass: 'Ⅴ',
    Tc: 'true',
    riskLevel: 'SL-B',
    description: 'SL-B：卡钻风险较高'
  }
]

/**
 * 数据处理工具函数
 * @param {number} value - 需要处理的数值
 * @param {number} decimal - 保留小数位数
 * @returns {number} 处理后的数值
 */
function formatNumber(value, decimal = 2) {
  return Number(value.toFixed(decimal))
}

/**
 * 根据钻进速度获取岩石强度信息
 * @param {number} advncSpd - 钻进速度
 * @returns {Object} 岩石强度信息
 */
const getRockStrengthInfo = advncSpd => {
  if (advncSpd <= 25) return { level: 90, desc: '极坚固' }
  if (advncSpd <= 50) return { level: 75, desc: '很坚固' }
  if (advncSpd <= 70) return { level: 60, desc: '坚固' }
  if (advncSpd <= 90) return { level: 45, desc: '比较坚固' }
  if (advncSpd <= 100) return { level: 30, desc: '中等坚固' }
  if (advncSpd <= 130) return { level: 15, desc: '较软' }
  return { level: 0, desc: '空洞' }
}

/**
 * 根据钻进速度和扭矩波动判断围岩等级
 * @param {number} speed - 钻进速度
 * @param {number} torqueDelta - 扭矩波动值
 * @param {number} standardTorque - 标准扭矩值
 * @returns {string} 围岩等级
 */
function determineRockClass(speed, torqueDelta, standardTorque) {
  const deltaRatio = torqueDelta / standardTorque

  for (const item of rockClassificationData) {
    if (speed <= item.speed) {
      if (item.deltaT.includes('< 0.7') && deltaRatio < 0.7) return item.output
      if (item.deltaT.includes('< 0.8') && deltaRatio < 0.8) return item.output
      if (item.deltaT.includes('< 0.9') && deltaRatio < 0.9) return item.output
      if (item.deltaT.includes('< 1') && deltaRatio < 1) return item.output
      if (item.deltaT.includes('> 1') && deltaRatio > 1) return item.output
      if (item.deltaT.includes('< 1.1') && deltaRatio < 1.1) return item.output
      if (item.deltaT.includes('> 1.1') && deltaRatio > 1.1) return item.output
    }
  }
  return 'Ⅵ类围岩' // 默认返回
}

/**
 * 判断岩石强度等级
 * @param {number} drillingRate - 钻进速度(cm/min)
 * @returns {string} 岩石强度等级
 */
function determineRockStrength(drillingRate) {
  // 钻进速度越慢，说明岩石越坚固
  // 根据rockStrengthData中的vi值判断
  for (const item of rockStrengthData) {
    if (drillingRate <= item.vi) {
      return item.output
    }
  }
  return '软岩'
}

/**
 * 判断岩石强度等级（包含抗压强度范围）
 * @param {number} drillingRate - 钻进速度(cm/min)
 * @returns {string} 岩石强度等级和抗压强度范围，格式：极坚固 200~250MPa
 */
function determineRockStrengthWithRange(drillingRate) {
  // 钻进速度越慢，说明岩石越坚固
  // 根据rockStrengthData中的vi值判断
  for (const item of rockStrengthData) {
    if (drillingRate <= item.vi) {
      // 格式化cs字符串，将"–"替换为"~"，并添加MPa单位
      let strengthRange = item.cs.replace('–', '~');
      if (strengthRange.includes('> 250')) {
        strengthRange = '>250MPa';
      } else if (strengthRange.includes('< 30')) {
        strengthRange = '<30MPa';
      } else {
        strengthRange = strengthRange + 'MPa';
      }
      return `${item.output} ${strengthRange}`;
    }
  }
  return '软岩 <20MPa'
}

/**
 * 实时计算函数的具体实现
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object} 计算结果
 */
function realTimeCalculation(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      throw new Error('输入数据格式错误')
    }

    // 获取最新的一条数据
    const latestData = data[data.length - 1]

    // 计算移动平均值（取最近10条数据）
    const recentData = data.slice(-10)
    const avgPropulsion =
      recentData.reduce((sum, item) => sum + Number(item.frcstPrs), 0) / recentData.length
    const avgTorque =
      recentData.reduce((sum, item) => sum + Number(item.rtnTq), 0) / recentData.length

    // 计算扭矩波动
    const torqueDelta =
      Math.max(...recentData.map(item => Number(item.rtnTq))) -
      Math.min(...recentData.map(item => Number(item.rtnTq)))

    // 判断空洞状态
    const isHollow =
      Number(latestData.frcstPrs) < avgPropulsion * 0.7 && Number(latestData.wtrPrsH) < 30

    // 计算钻进效率
    const drillingEfficiency = formatNumber(
      (Number(latestData.advncSpd) / Number(latestData.frcstPrs)) * 100
    )

    // 判断围岩等级
    const rockClass = determineRockClass(Number(latestData.advncSpd), torqueDelta, avgTorque)

    // 判断岩石强度
    const rockStrength = determineRockStrength(Number(latestData.advncSpd))

    return {
      currentDepth: Number(latestData.dpth),
      drillingEfficiency,
      avgPropulsion: formatNumber(avgPropulsion),
      avgTorque: formatNumber(avgTorque),
      torqueDelta: formatNumber(torqueDelta),
      isHollow,
      rockClass,
      rockStrength,
      timestamp: latestData.collection_at
    }
  } catch (error) {
    console.error('实时计算出错:', error)
    return null
  }
}

/**
 * 数据精度处理
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 数据精度处理后的数据
 */
function dataPrecisionProcessing(data) {
  try {
    // 循环数组，处理其中的数据，返回处理后的数据列表
    // 旋转速度 rtnSpd / 10
    // 钻进速度 advncSpd / 100
    // 液压压力 hydrPrs / 10
    // 旋转压力 rtnPrs / 10
    // 推进压力 frcstPrs / 10
    // 孔倾角 hIAng / 10
    // 工作时长低位 lwWrk / 10
    const processedData = data.map(item => {
      return {
        ...item,
        rtnSpd: parseFloat((Number(item.rtnSpd) / 10).toFixed(2)),
        advncSpd: parseFloat((Number(item.advncSpd) / 100).toFixed(3)),
        hydrPrs: parseFloat((Number(item.hydrPrs) / 10).toFixed(2)),
        rtnPrs: parseFloat((Number(item.rtnPrs) / 10).toFixed(2)),
        frcstPrs: parseFloat((Number(item.frcstPrs) / 10).toFixed(2)),
        hIAng: parseFloat((Number(item.hIAng) / 10).toFixed(2))
      }
    })
    return processedData
  } catch (error) {
    console.error('生成饼图数据出错:', error)
    return null
  }
}

/**
 * 生成岩石性质饼图数据
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 饼图数据
 */
function generateRockyNaturePieChartData(data) {
  try {
    // 统计不同钻进速度范围的数量
    const strengthCount = data.reduce((acc, cur) => {
      const rockStrengthInfo = getRockStrengthInfo(Number(cur.advncSpd))
      let strength = rockStrengthInfo.desc
      acc[strength] = (acc[strength] || 0) + 1
      return acc
    }, {})

    // 计算百分比
    const total = Object.values(strengthCount).reduce((a, b) => a + b, 0)
    const pieData = Object.entries(strengthCount).map(([strength, count]) => ({
      value: formatNumber((count / total) * 100),
      name: strength
    }))

    return pieData.sort((a, b) => b.value - a.value)
  } catch (error) {
    console.error('生成饼图数据出错:', error)
    return null
  }
}

/**
 * 生成围岩统计柱状图数据
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 柱状图数据
 */
function generatePerimeterRockStatisticsBarChartData(data) {
  try {
    // 根据钻进速度和扭矩波动统计围岩等级
    const rockClassCount = data.reduce((acc, cur) => {
      const drillingRate = Number(cur.advncSpd)
      const torqueDelta = Math.abs(Number(cur.rtnTq) - Number(cur.frcstPrs))
      const standardTorque = Number(cur.rtnTq)

      const rockClass = determineRockClass(drillingRate, torqueDelta, standardTorque)
      acc[rockClass] = (acc[rockClass] || 0) + 1
      return acc
    }, {})

    // 计算百分比并格式化数据
    const total = Object.values(rockClassCount).reduce((a, b) => a + b, 0)
    if (total === 0) {
      console.warn('围岩统计柱状图数据: 没有有效的岩石类别被统计, 总数为0。')
      return [] // Return empty array if no data to show
    }
    const existingRockClasses = Object.keys(rockClassCount)
    // 找到值最大的岩石类别的名称
    let classNameWithMaxValue = null
    let maxValue = -Infinity
    // Iterate over the rockClassCount to find the className with the highest value
    for (const className in rockClassCount) {
      if (rockClassCount.hasOwnProperty(className)) {
        // Ensure it's an own property
        if (rockClassCount[className] > maxValue) {
          maxValue = rockClassCount[className]
          classNameWithMaxValue = className
        }
      }
    }
    return existingRockClasses.map(className => ({
      value: formatNumber(((rockClassCount[className] || 0) / total) * 100),
      name: className,
      itemStyle: {
        color: className === classNameWithMaxValue ? '#FF6E76' : '#5470C6'
      }
    }))
  } catch (error) {
    console.error('围岩统计柱状图数据出错:', error)
    return null
  }
}

/**
 * 地质报告生成函数
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object|null} 地质报告数据
 */
function generateGeologicAnalysisReport(data) {
  try {
    // TODO: 实现地质报告生成逻辑
    // 1. 分析输入数据
    // 2. 生成报告内容
    // 3. 返回报告数据
    /**
     * 报告中的空洞监测逻辑说明：
     *
     * Hollow:H1 (空洞开始点)
     * 触发条件：
     * 1. 实时推进压力 < (10秒内推进压力移动平均值 * 70%)
     * 2. 高压水压力 < 30bar
     * 动作：记录当前实时推进值为P标准
     *
     * Hollow:H (中间状态)
     * 持续监测中
     *
     * Hollow:H2 (空洞结束点)
     * 触发条件：
     * 1. 实时推进压力 > (P标准值 * 80%) 或
     * 2. 高压水压力 > 30bar
     * 动作：
     * 1. 计算空洞长度 (H2点深度 - H1点深度)
     * 2. 重新开始计算移动平均值
     * 3. 更新P标准值，直到新的H1点出现
     */
    let report = []
    report.push({
      name: '地质构造',
      description:
        '地质构造 III、VI类围岩占比87%，施工过程中遇到16～25米断层或褶皱，存在大型断层带去呀，岩石性质差异大。',
      suggestion:
        '建议：使用大扭矩钻进，钻进过程中加强扭矩监控，及时调整钻进参数，避免卡钻事故的发生。'
    })
    report.push({
      name: '地下水状况',
      description: '30米处，出现大量涌水现象。',
      suggestion: '建议：使用超前钻探结合注浆堵水技术预先处理潜在涌水点。'
    })
    report.push({
      name: '岩石变化',
      description: '从坚硬岩石到软弱底层的突然转变（25-30米、50-60米）。',
      suggestion: '建议：选择合适刀盘配置，优化掘进参数；监测岩石强度变化。'
    })
    report.push({
      name: '空洞溶洞',
      description: '40米-55米，地下存在空洞或溶洞',
      suggestion: '建议：对空洞区段，采取预填充或其他加固措施。'
    })
    report.push({
      name: '有害气体',
      description: '45米处，检测到甲烷类易燃气体。',
      suggestion:
        '建议：安装气体检测报警装置，实时监控有害气体浓度；确保良好的通风条件，降低爆炸风险。'
    })

    return report
  } catch (error) {
    console.error('生成地质报告出错:', error)
    return null
  }
}

/**
 * 生成岩层分布折线图数据
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object} 折线图数据
 */
function generateStrataDistributionLineChartData(data) {
  try {
    // 1. 确保 data 是一个数组
    if (!Array.isArray(data)) {
      console.error('Error: Input data is not an array. Received:', data)
      // 根据你的业务逻辑，可以返回空数组或其他错误指示
      return {
        data: [],
        极坚固: 25,
        很坚固: 50,
        坚固: 70,
        比较坚固: 90,
        中等坚固: 100,
        较软: 130,
        空洞: 150
      }
    }
    const processedData = data
      .map(item => {
        // 2. 确保 item 是一个对象并且具有必要的属性
        // 可以使用可选链 (?.）和空值合并运算符 (??) 来增加安全性
        const depth = item?.dpth ?? null // 如果 item 或 item.dpth 是 null/undefined, depth 为 null
        const advncSpd = item?.advncSpd
        return {
          depth: depth,
          value: advncSpd !== undefined && advncSpd !== null ? Number(advncSpd) : null // 确保 advncSpd 存在再转换
        }
      })
      .filter(item => item.depth !== null) // 可选：过滤掉处理失败的项
    return {
      data: processedData,
      // 岩石强度分界点 (这些定义本身不会导致 .map 报错)
      极坚固: 25, // 钻进速度 <= 25 cm/min
      很坚固: 50, // 25 < 钻进速度 <= 50 cm/min
      坚固: 70, // 50 < 钻进速度 <= 70 cm/min
      比较坚固: 90, // 70 < 钻进速度 <= 90 cm/min
      中等坚固: 100, // 90 < 钻进速度 <= 100 cm/min
      较软: 130, // 100 < 钻进速度 <= 130 cm/min
      空洞: 150 // 钻进速度 > 130 cm/min (实际应该是 >= 130，或者 > 上一个阈值)
      // 注意：上面的注释描述的 "空洞" 区间与 "较软" 有重叠，应为 > 130
      // 或者更清晰的定义：
      // 极坚固: value <= 25
      // 很坚固: value > 25 && value <= 50
      // ...
      // 较软: value > 100 && value <= 130
      // 空洞: value > 130
    }
  } catch (error) {
    console.error('岩层分布折线图数据出错:', error)
    return null
  }
}

/**
 * 工况矩阵图生成函数
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array|null} 工况矩阵图数据列表
 */
function generateConditionMatrixChartData(data) {
  try {
    // 确保输入数据有效
    if (!Array.isArray(data) || data.length === 0) {
      throw new Error('输入数据格式错误')
    }

    // 按照第二段代码的格式处理数据
    const result = data.map(item => {
      // 1. 获取并转换基础数值
      const workMode = Number(item.mode)
      const drillingDepth = Number(item.dpth)
      const rotationTorque = Number(item.rtnTq)
      const propulsion = Number(item.frcstPrs)
      const rotationalSpeed = Number(item.rtnSpd)
      const drillingRate = Number(item.advncSpd)

      // 2. 计算围岩等级
      const torqueDelta = Math.abs(rotationTorque - propulsion)
      const rockClass = determineRockClass(drillingRate, torqueDelta, rotationTorque)

      // 3. 计算岩石强度级别
      const rockStrength = determineRockStrength(drillingRate)

      // 4. 判断预警级别 - 按照示例中的逻辑
      let warningLevel = '无预警'
      if (rotationTorque > 200) warningLevel = '高风险预警'
      else if (rotationTorque > 150) warningLevel = '中风险预警'
      else if (rotationTorque > 100) warningLevel = '低风险预警'

      // 5. 返回与示例中相同格式的数组
      return [
        workMode, // 工作模式
        drillingDepth, // 钻孔深度CM
        rotationTorque, // 旋转扭矩
        propulsion, // 推进力
        rotationalSpeed, // 旋转速度
        drillingRate, // 钻进速度
        rockStrength, // 岩石强度级别
        rockClass, // 围岩等级
        warningLevel, // 地质预警
        warningLevel // 重复值(用于显示)
      ]
    })

    return result
  } catch (error) {
    console.error('工况矩阵图数据出错:', error)
    return null
  }
}

/**
 * 钻进数据生成函数
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object|null} 钻进数据列表
 */
function generateDrillingData(data) {
  try {
    return data.map(item => {
      // 1. 计算岩石强度 (1-100的评分)，根据钻进速度技术
      const advncSpd = Number(item.advncSpd)
      const rockStrengthInfo = getRockStrengthInfo(advncSpd)
      const rockStrengthLevel = rockStrengthInfo.level
      const rockStrengthDesc = rockStrengthInfo.desc

      // 2. 计算围岩等级 (1-100的评分)，根据钻进速度、推进力、旋转扭矩
      const torqueDelta = Math.abs(Number(item.rtnTq) - Number(item.frcstPrs))
      const standardTorque = Number(item.rtnTq)
      const rockClass = determineRockClass(advncSpd, torqueDelta, standardTorque)
      const rockGradeLevel = (() => {
        switch (rockClass) {
          case 'Ⅰ类围岩':
            return 90
          case 'Ⅱ类围岩':
            return 75
          case 'Ⅲ类围岩':
            return 60
          case 'Ⅳ类围岩':
            return 45
          case 'Ⅴ类围岩':
            return 30
          case 'Ⅵ类围岩':
            return 15
          default:
            return 0
        }
      })()

      // 3. 计算地质预警等级 (0-3: 无预警、低风险、中风险、高风险)
      const geologicalWarningLevel = (() => {
        // 检查多个地质风险因素
        let riskScore = 0

        // 因素1: 推进压力异常
        if (Number(item.frcstPrs) > 200) riskScore += 2
        else if (Number(item.frcstPrs) > 150) riskScore += 1

        // 因素2: 扭矩异常
        if (Number(item.rtnTq) > 180) riskScore += 2
        else if (Number(item.rtnTq) > 150) riskScore += 1

        // 因素3: 钻进速度异常
        if (advncSpd > 130 || advncSpd < 10) riskScore += 1

        // 因素4: 低压水压异常
        if (Number(item.wtrPrsL) < 30) riskScore += 1

        // 综合评定预警等级
        if (riskScore >= 4) return 3 // 高风险
        if (riskScore >= 2) return 2 // 中风险
        if (riskScore >= 1) return 1 // 低风险
        return 0 // 无预警
      })()

      // 4. 计算设备预警等级 (0-3: 正常、轻微、中度、严重)
      const equipmentWarningLevel = (() => {
        // 检查多个设备参数
        let warningScore = 0

        // 参数1: 转速异常
        if (Math.abs(Number(item.rtnSpd) - 100) > 30) warningScore += 1

        // 参数2: 扭矩过载
        if (Number(item.rtnTq) > 200) warningScore += 2

        // 参数3: 推进压力过载
        if (Number(item.frcstPrs) > 220) warningScore += 2

        // 参数4: 水压异常(高压水压力、低压水压力)
        if (Number(item.wtrPrsH) > 180 || Number(item.wtrPrsL) < 20) warningScore += 1

        // 综合评定设备预警等级
        if (warningScore >= 4) return 3 // 严重
        if (warningScore >= 2) return 2 // 中度
        if (warningScore >= 1) return 1 // 轻微
        return 0 // 正常
      })()

      // 返回原始数据加计算结果
      return {
        ...item,
        rockStrengthLevel: rockStrengthLevel, // 岩石强度评分(1-100)
        rockStrengthDesc: rockStrengthDesc, // 岩石强度描述
        rockGradeDesc: rockClass, // 围岩等级
        rockGradeLevel: rockGradeLevel, // 围岩等级评分(1-100)
        geologicalWarning: geologicalWarningLevel, // 地质预警等级(0-3)
        equipmentWarning: equipmentWarningLevel // 设备预警等级(0-3)
      }
    })
  } catch (error) {
    console.error('生成钻进数据出错:', error)
    return null
  }
}

/**
 * 卡钻检测算法 - 检测整组数据中的卡钻事件和所有卡钻点
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, " ": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object} {events: Array, stuckPoints: Array} - 卡钻事件列表和所有卡钻点
 */
function generateDetectStuckEventsData(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('卡钻检测: 输入数据为空或格式错误')
      return {
        events: [],
        stuckPoints: [],
        summary: {
          totalEvents: 0,
          totalStuckPoints: 0,
          totalDuration: 0
        }
      }
    }

    console.log(`卡钻检测开始: 输入数据量 ${data.length} 条`)

    const stuckEvents = []
    const stuckPoints = [] // 存储所有卡钻点
    const minStuckDuration = 5000 // 5秒 = 5000毫秒

    let currentStuckStart = null
    let currentStuckStartIndex = null
    let currentStuckPoints = [] // 当前卡钻事件中的所有点

    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      const advncSpd = Number(item.advncSpd)
      const rtnSpd = Number(item.rtnSpd)
      const currentTime = new Date(item.collectionAt).getTime()

      // 检查是否满足卡钻条件（钻进速度=0 且 旋转速度=0）
      const isStuckCondition = advncSpd === 0 && rtnSpd === 0

      if (isStuckCondition) {
        // 开始新的卡钻事件
        if (currentStuckStart === null) {
          currentStuckStart = currentTime
          currentStuckStartIndex = i
          currentStuckPoints = []
        }

        // 记录当前卡钻点
        currentStuckPoints.push({
          ...item,
          index: i,
          isStuck: true
        })
      } else {
        // 结束当前卡钻事件（如果存在）
        if (currentStuckStart !== null) {
          const stuckDuration = currentTime - currentStuckStart

          // 检查持续时间是否满足条件（≥5秒）
          if (stuckDuration >= minStuckDuration) {
            const stuckEvent = {
              startTime: new Date(currentStuckStart).toISOString(),
              endTime: new Date(currentTime).toISOString(),
              duration: stuckDuration / 1000, // 转换为秒
              startIndex: currentStuckStartIndex,
              endIndex: i - 1,
              startDepth: Number(data[currentStuckStartIndex].dpth),
              endDepth: Number(data[i - 1].dpth),
              description: `卡钻事件: 持续${(stuckDuration / 1000).toFixed(1)}秒`,
              points: currentStuckPoints // 包含该事件中的所有卡钻点
            }
            stuckEvents.push(stuckEvent)

            // 将卡钻点添加到总列表中
            stuckPoints.push(...currentStuckPoints)
          }

          // 重置卡钻状态
          currentStuckStart = null
          currentStuckStartIndex = null
          currentStuckPoints = []
        }
      }
    }

    // 处理数据结束时仍在卡钻状态的情况
    if (currentStuckStart !== null && data.length > 0) {
      const lastTime = new Date(data[data.length - 1].collectionAt).getTime()
      const stuckDuration = lastTime - currentStuckStart

      if (stuckDuration >= minStuckDuration) {
        const stuckEvent = {
          startTime: new Date(currentStuckStart).toISOString(),
          endTime: new Date(lastTime).toISOString(),
          duration: stuckDuration / 1000,
          startIndex: currentStuckStartIndex,
          endIndex: data.length - 1,
          startDepth: Number(data[currentStuckStartIndex].dpth),
          endDepth: Number(data[data.length - 1].dpth),
          description: `卡钻事件: 持续${(stuckDuration / 1000).toFixed(1)}秒（至数据结束）`,
          points: currentStuckPoints
        }
        stuckEvents.push(stuckEvent)

        // 将卡钻点添加到总列表中
        stuckPoints.push(...currentStuckPoints)
      }
    }

    console.log(
      `卡钻检测完成: 发现 ${stuckEvents.length} 个卡钻事件，共 ${stuckPoints.length} 个卡钻点`
    )

    return {
      events: stuckEvents, // 卡钻事件列表
      stuckPoints: stuckPoints, // 所有卡钻点
      summary: {
        totalEvents: stuckEvents.length,
        totalStuckPoints: stuckPoints.length,
        totalDuration: stuckEvents.reduce((sum, event) => sum + event.duration, 0)
      }
    }
  } catch (error) {
    console.error('卡钻检测过程中发生错误:', error)
    return {
      events: [],
      stuckPoints: [],
      summary: {
        totalEvents: 0,
        totalStuckPoints: 0,
        totalDuration: 0
      }
    }
  }
}

/**
 * 钻进速度突变检测算法
 * 检测钻进速度的突然变化，识别地质条件变化或设备异常
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object} {upwardEvents: Array, downwardEvents: Array, upwardPoints: Array, downwardPoints: Array} - 突变事件列表和所有突变点
 */
function generateDrillingSpeedMutationData(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('突变检测: 输入数据为空或格式错误')
      return {
        upwardEvents: [],
        downwardEvents: [],
        upwardPoints: [],
        downwardPoints: [],
        summary: {
          totalEvents: 0,
          totalMutationPoints: 0,
          upwardMutations: 0,
          downwardMutations: 0
        }
      }
    }

    console.log(`突变检测开始: 输入数据量 ${data.length} 条`)

    const mutationEvents = []
    const mutationPoints = [] // 存储所有突变点
    const timeWindow = 3000 // 3秒 = 3000毫秒

    for (let i = 0; i < data.length; i++) {
      const currentItem = data[i]
      const currentAdvncSpd = Number(currentItem.advncSpd)
      const currentTime = new Date(currentItem.collectionAt).getTime()

      // 跳过无效数据
      if (isNaN(currentAdvncSpd) || !isFinite(currentAdvncSpd)) {
        continue
      }

      // 收集前3秒的数据（不包括当前点）
      const previousData = []
      for (let j = i - 1; j >= 0; j--) {
        const prevTime = new Date(data[j].collectionAt).getTime()
        const timeDiff = currentTime - prevTime

        // 如果时间差超过3秒，停止收集
        if (timeDiff > timeWindow) {
          break
        }

        const prevAdvncSpd = Number(data[j].advncSpd)
        if (!isNaN(prevAdvncSpd) && isFinite(prevAdvncSpd)) {
          previousData.push(prevAdvncSpd)
        }
      }

      // 需要至少有前3秒的数据才能进行突变检测
      if (previousData.length === 0) {
        continue
      }

      // 计算前3秒钻进速度的平均值
      const avgPreviousSpeed = previousData.reduce((sum, speed) => sum + speed, 0) / previousData.length

      let isMutation = false
      let mutationType = ''
      let description = ''

      // 检测向上突变：advncSpd > 1 且 当前速度 > 前3s平均值的1.4倍 且 差值大于 0.3
      if (
        currentAdvncSpd > 1 &&
        currentAdvncSpd > avgPreviousSpeed * 1.4 &&
        Math.abs(avgPreviousSpeed - currentAdvncSpd) > 0.3
      ) {
        isMutation = true
        mutationType = 'upward'
        description = `向上突变: 钻进速度从${avgPreviousSpeed.toFixed(2)}突增至${currentAdvncSpd.toFixed(2)} (增幅${((currentAdvncSpd / avgPreviousSpeed - 1) * 100).toFixed(1)}%)`
        console.log(
          `检测到向上突变: 当前=${currentAdvncSpd}, 平均=${avgPreviousSpeed.toFixed(2)}, 阈值=${(avgPreviousSpeed * 1.3).toFixed(2)}`
        )
      }
      // 检测向下突变：advncSpd < 0.8 且 当前速度 < 前3s平均值的0.6倍 且 差值大于 0.3
      else if (
        currentAdvncSpd < 0.8 &&
        currentAdvncSpd < avgPreviousSpeed * 0.6 &&
        Math.abs(avgPreviousSpeed - currentAdvncSpd) > 0.3
      ) {
        isMutation = true
        mutationType = 'downward'
        description = `向下突变: 钻进速度从${avgPreviousSpeed.toFixed(2)}骤降至${currentAdvncSpd.toFixed(2)} (降幅${((1 - currentAdvncSpd / avgPreviousSpeed) * 100).toFixed(1)}%)`
        console.log(
          `检测到向下突变: 当前=${currentAdvncSpd}, 平均=${avgPreviousSpeed.toFixed(2)}, 阈值=${(avgPreviousSpeed * 0.7).toFixed(2)}`
        )
      }

      if (isMutation) {
        // 创建当前突变点
        const currentMutationPoint = {
          ...currentItem,
          index: i,
          isMutation: true,
          mutationType: mutationType,
          currentSpeed: currentAdvncSpd,
          previousAvgSpeed: avgPreviousSpeed,
          speedRatio: currentAdvncSpd / avgPreviousSpeed,
          description: description,
          role: 'current' // 标记为当前点
        }

        // 创建前一个突变点（突变必须包含前一个点，无论是否已被标记）
        let previousMutationPoint = null
        if (i > 0) {
          const previousItem = data[i - 1]
          const previousAdvncSpd = Number(previousItem.advncSpd)

          if (!isNaN(previousAdvncSpd) && isFinite(previousAdvncSpd)) {
            previousMutationPoint = {
              ...previousItem,
              index: i - 1,
              isMutation: true,
              mutationType: mutationType,
              currentSpeed: previousAdvncSpd,
              previousAvgSpeed: avgPreviousSpeed,
              speedRatio: currentAdvncSpd / avgPreviousSpeed, // 使用当前点的比率
              description: `突变前点: 速度${previousAdvncSpd.toFixed(2)}`,
              role: 'previous' // 标记为前一个点
            }
          }
        }

        // 添加突变点到列表（只添加当前点到全局列表，前一个点只用于事件）
        // 避免重复添加前一个点到全局列表
        const alreadyExists = mutationPoints.some(point => point.index === i - 1)
        if (previousMutationPoint && !alreadyExists) {
          mutationPoints.push(previousMutationPoint)
        }
        mutationPoints.push(currentMutationPoint)

        // 每次检测到突变都创建独立的事件，确保每个事件包含2个点
        const eventPoints = []
        if (previousMutationPoint) {
          eventPoints.push(previousMutationPoint)
        }
        eventPoints.push(currentMutationPoint)

        const mutationEvent = {
          startTime: previousMutationPoint ? previousMutationPoint.collectionAt : currentItem.collectionAt,
          endTime: currentItem.collectionAt,
          startIndex: previousMutationPoint ? i - 1 : i,
          endIndex: i,
          startDepth: previousMutationPoint ? Number(previousMutationPoint.dpth) : Number(currentItem.dpth),
          endDepth: Number(currentItem.dpth),
          mutationType: mutationType,
          description: description,
          points: eventPoints
        }
        mutationEvents.push(mutationEvent)
      }
    }

    // 按突变类型分类事件和点
    const upwardEvents = mutationEvents.filter(event => event.mutationType === 'upward')
    const downwardEvents = mutationEvents.filter(event => event.mutationType === 'downward')
    const upwardPoints = mutationPoints.filter(p => p.mutationType === 'upward')
    const downwardPoints = mutationPoints.filter(p => p.mutationType === 'downward')

    // 统计不同类型的突变
    const upwardMutations = upwardPoints.length
    const downwardMutations = downwardPoints.length

    console.log(
      `突变检测完成: 发现 ${mutationEvents.length} 个突变事件，共 ${mutationPoints.length} 个突变点 (向上: ${upwardMutations}, 向下: ${downwardMutations})`
    )

    return {
      // 按类型分类的数据
      upwardEvents: upwardEvents, // 向上突变事件列表
      downwardEvents: downwardEvents, // 向下突变事件列表
      upwardPoints: upwardPoints, // 向上突变点列表
      downwardPoints: downwardPoints, // 向下突变点列表

      // 统计信息
      summary: {
        totalEvents: mutationEvents.length,
        totalMutationPoints: mutationPoints.length,
        upwardMutations: upwardMutations,
        downwardMutations: downwardMutations,
        upwardEventsCount: upwardEvents.length,
        downwardEventsCount: downwardEvents.length
      }
    }
  } catch (error) {
    console.error('突变检测过程中发生错误:', error)
    return {

      // 按类型分类的数据
      upwardEvents: [],
      downwardEvents: [],
      upwardPoints: [],
      downwardPoints: [],

      // 统计信息
      summary: {
        totalEvents: 0,
        totalMutationPoints: 0,
        upwardMutations: 0,
        downwardMutations: 0,
        upwardEventsCount: 0,
        downwardEventsCount: 0
      }
    }
  }
}

/**
 * 钻探记录表数据生成算法
 * 专门为钻探记录表组件生成数据，包含岩石强度计算、突进检测和卡钻检测
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 钻探记录表数据
 */
function generateDrillingRecordData(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('钻探记录数据生成: 输入数据为空或格式错误')
      return []
    }

    console.log(`钻探记录数据生成开始: 输入数据量 ${data.length} 条`)

    // 1. 首先获取卡钻检测结果
    const stuckDetectionResult = generateDetectStuckEventsData(data)
    const stuckPoints = stuckDetectionResult.stuckPoints || []

    // 2. 获取突进检测结果
    const mutationDetectionResult = generateDrillingSpeedMutationData(data)
    const mutationPoints = [...(mutationDetectionResult.upwardPoints || []), ...(mutationDetectionResult.downwardPoints || [])]

    // 3. 创建卡钻和突进点的快速查找映射
    const stuckPointsMap = new Map()
    stuckPoints.forEach(point => {
      const key = `${point.collectionAt}_${point.dpth}`
      stuckPointsMap.set(key, point)
    })

    const mutationPointsMap = new Map()
    mutationPoints.forEach(point => {
      const key = `${point.collectionAt}_${point.dpth}`
      mutationPointsMap.set(key, point)
    })

    // 4. 处理每个数据点，添加钻探记录表所需的字段
    const processedData = data.map((item) => {
      // 基础数据转换
      const advncSpd = Number(item.advncSpd)

      // 计算岩石强度（带抗压强度范围）
      const rockStrengthDesc = determineRockStrengthWithRange(advncSpd)

      // 检查是否为卡钻点
      const pointKey = `${item.collectionAt}_${item.dpth}`
      const isStuckPoint = stuckPointsMap.has(pointKey)
      const stuckInfo = stuckPointsMap.get(pointKey)

      // 检查是否为突进点
      const isMutationPoint = mutationPointsMap.has(pointKey)
      const mutationInfo = mutationPointsMap.get(pointKey)

      // 生成地质描述
      let geologicalDesc = '无卡钻 无突进 钻进正常'

      if (isStuckPoint) {
        geologicalDesc = `卡钻事件: ${stuckInfo.description || '钻进停滞'}`
      } else if (isMutationPoint) {
        geologicalDesc = `速度突变: ${mutationInfo.description || '钻进速度异常变化'}`
      } 

      // 计算含水情况（基于水压力）
      // const wtrPrsH = Number(item.wtrPrsH || 0)
      let waterCondition = ''
      // if (wtrPrsH > 100) {
      //   waterCondition = '含水丰富'
      // } else if (wtrPrsH > 50) {
      //   waterCondition = '少量含水'
      // } else {
      //   waterCondition = '干燥'
      // }

      // 返回钻探记录表格式的数据 - 只返回前端需要的字段
      return {
        // 基础数据字段
        collectionAt: item.collectionAt, // 采集时间
        deviceSn: item.deviceSn, // 设备序列号
        dpth: item.dpth, // 钻进深度
        advncSpd: item.advncSpd, // 钻进速度
        rtnTq: item.rtnTq, // 旋转扭矩
        wtrPrsH: item.wtrPrsH, // 水压力
        frcstKn: item.frcstKn, // 推进力
        rtnSpd: item.rtnSpd, // 旋转速度
        // 项目信息字段（从原始数据中获取）
        tunnelName: item.tunnelName || '', // 隧道名称
        hlNum: item.hlNum || '', // 孔编号
        hlAng: item.hlAng || '', // 孔倾角
        // 钻探记录表专用字段
        waterCondition: waterCondition, // 含水情况
        rockStrengthDesc: rockStrengthDesc, // 岩石强度描述 (如: "极坚固 200~250MPa")
        geologicalDesc: geologicalDesc, // 地质情况描述
        // 标记字段
        isStuckPoint: isStuckPoint, // 是否为卡钻点
        isMutationPoint: isMutationPoint // 是否为突进点
      }
    })

    return processedData

  } catch (error) {
    console.error('钻探记录数据生成出错:', error)
    return null
  }
}

/**
 * 钻进深度时序分析算法
 * 展示不同采集时间的钻进深度，并计算该点是否卡钻、是否突进
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 钻进深度时序分析数据
 */
function generateDrillingDepthTimeSeriesData(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('钻进深度时序分析: 输入数据为空或格式错误')
      return []
    }

    console.log(`钻进深度时序分析开始: 输入数据量 ${data.length} 条`)

    // 1. 首先获取卡钻检测结果
    const stuckDetectionResult = generateDetectStuckEventsData(data)
    const stuckPoints = stuckDetectionResult.stuckPoints || []

    // 2. 获取突进检测结果
    const mutationDetectionResult = generateDrillingSpeedMutationData(data)
    const mutationPoints = [...(mutationDetectionResult.upwardPoints || []), ...(mutationDetectionResult.downwardPoints || [])]

    // 3. 创建卡钻和突进点的快速查找映射
    const stuckPointsMap = new Map()
    stuckPoints.forEach(point => {
      const key = `${point.collectionAt}_${point.dpth}`
      stuckPointsMap.set(key, point)
    })

    const mutationPointsMap = new Map()
    mutationPoints.forEach(point => {
      const key = `${point.collectionAt}_${point.dpth}`
      mutationPointsMap.set(key, point)
    })

    // 4. 按时间排序数据
    const sortedData = [...data].sort((a, b) => new Date(a.collectionAt) - new Date(b.collectionAt))

    // 5. 处理每个数据点，生成时序分析数据
    const timeSeriesData = sortedData.map((item, index) => {
      // 基础数据转换
      const collectionTime = new Date(item.collectionAt)
      const depth = Number(item.dpth)
      const advncSpd = Number(item.advncSpd)
      const rtnSpd = Number(item.rtnSpd)
      const rtnTq = Number(item.rtnTq)
      const frcstKn = Number(item.frcstKn)
      const wtrPrsH = Number(item.wtrPrsH)

      // 检查是否为卡钻点
      const pointKey = `${item.collectionAt}_${item.dpth}`
      const isStuckPoint = stuckPointsMap.has(pointKey)

      // 检查是否为突进点
      const isMutationPoint = mutationPointsMap.has(pointKey)

      // 计算深度变化率（如果有前一个点）
      let depthChangeRate = 0
      let timeInterval = 0
      if (index > 0) {
        const prevItem = sortedData[index - 1]
        const prevDepth = Number(prevItem.dpth)
        const prevTime = new Date(prevItem.collectionAt)

        timeInterval = (collectionTime - prevTime) / 1000 // 时间间隔（秒）
        const depthChange = depth - prevDepth

        if (timeInterval > 0) {
          depthChangeRate = depthChange / timeInterval // 深度变化率 (cm/s)
        }
      }

      // 返回时序分析数据
      return {
        // 时间和深度信息
        collectionAt: item.collectionAt, // 采集时间
        depth: depth, // 钻进深度 (cm)
        depthChangeRate: formatNumber(depthChangeRate, 3), // 深度变化率 (cm/s)
        timeInterval: formatNumber(timeInterval, 2), // 时间间隔 (s)

        // 基础钻进参数
        advncSpd: advncSpd, // 钻进速度 (cm/min)
        rtnSpd: rtnSpd, // 旋转速度 (rpm)
        rtnTq: rtnTq, // 旋转扭矩 (Nm)
        frcstKn: frcstKn, // 推进力 (kN)
        wtrPrsH: wtrPrsH, // 水压力 (bar)

        // 状态标记
        isStuckPoint: isStuckPoint, // 是否为卡钻点
        isMutationPoint: isMutationPoint, // 是否为突进点
      }
    })

    // 返回包含数据和统计信息的结果
    return timeSeriesData

  } catch (error) {
    console.error('钻进深度时序分析出错:', error)
    return data
  }
}

/**
 * 钻进实时数据生成函数
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Object|null} 钻进数据列表
 */
function generateDrillingRealTimeData(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('实时钻进数据生成: 输入数据为空或格式错误')
      return []
    }

    // 获取最后一个数据点（最新数据）
    const lastItem = data[data.length - 1]

    // 1. 计算最后一个点的岩石强度 (1-100的评分)，根据钻进速度
    const advncSpd = Number(lastItem.advncSpd)
    const rockStrengthInfo = getRockStrengthInfo(advncSpd)
    const rockStrengthLevel = rockStrengthInfo.level
    const rockStrengthDesc = rockStrengthInfo.desc

    // 2. 计算最后一个点的围岩等级 (1-100的评分)，根据钻进速度、推进力、旋转扭矩
    const torqueDelta = Math.abs(Number(lastItem.rtnTq) - Number(lastItem.frcstPrs))
    const standardTorque = Number(lastItem.rtnTq)
    const rockClass = determineRockClass(advncSpd, torqueDelta, standardTorque)
    const rockGradeLevel = (() => {
      switch (rockClass) {
        case 'Ⅰ类围岩':
          return 90
        case 'Ⅱ类围岩':
          return 75
        case 'Ⅲ类围岩':
          return 60
        case 'Ⅳ类围岩':
          return 45
        case 'Ⅴ类围岩':
          return 30
        case 'Ⅵ类围岩':
          return 15
        default:
          return 0
      }
    })()

    // 3. 利用连续的5个点进行卡钻检测
    const stuckDetectionResult = generateDetectStuckEventsData(data)
    const stuckPoints = stuckDetectionResult.stuckPoints || []

    // 4. 利用连续的5个点进行突进检测
    const mutationDetectionResult = generateDrillingSpeedMutationData(data)
    const upwardPoints = mutationDetectionResult.upwardPoints || []
    const downwardPoints = mutationDetectionResult.downwardPoints || []

    // 5. 检查最后一个点是否为卡钻点
    const lastPointKey = `${lastItem.collectionAt}_${lastItem.dpth}`
    const isLastPointStuck = stuckPoints.some(point =>
      `${point.collectionAt}_${point.dpth}` === lastPointKey
    )

    // 6. 检查最后一个点是否为上突进点
    const isLastPointUpMutation = upwardPoints.some(point =>
      `${point.collectionAt}_${point.dpth}` === lastPointKey
    )

    // 7. 检查最后一个点是否为下突进点
    const isLastPointDownMutation = downwardPoints.some(point =>
      `${point.collectionAt}_${point.dpth}` === lastPointKey
    )

    // 8. 综合判断是否为突进点
    const isLastPointMutation = isLastPointUpMutation || isLastPointDownMutation

    // 9. 计算预警等级
    let geologicalWarningLevel = 0
    let equipmentWarningLevel = 0

    // 基于岩石强度和围岩等级计算地质预警
    if (rockStrengthLevel <= 15 || rockGradeLevel <= 30) {
      geologicalWarningLevel = 2 // 中风险
    } else if (rockStrengthLevel <= 30 || rockGradeLevel <= 45) {
      geologicalWarningLevel = 1 // 低风险
    }

    // 基于卡钻和突进情况计算设备预警
    if (isLastPointStuck) {
      equipmentWarningLevel = 3 // 高风险
    } else if (isLastPointMutation) {
      equipmentWarningLevel = 2 // 中风险
    }

    console.log(`实时数据处理完成 - 岩石强度: ${rockStrengthLevel}, 围岩等级: ${rockGradeLevel}, 卡钻: ${isLastPointStuck}, 突进: ${isLastPointMutation}`)

    // 10. 返回只包含最后一个点的数组
    return [{
      ...lastItem,
      rockStrengthLevel: rockStrengthLevel, // 岩石强度评分(1-100)
      rockStrengthDesc: rockStrengthDesc, // 岩石强度描述
      rockGradeDesc: rockClass, // 围岩等级
      rockGradeLevel: rockGradeLevel, // 围岩等级评分(1-100)
      geologicalWarning: geologicalWarningLevel, // 地质预警等级(0-3)
      equipmentWarning: equipmentWarningLevel, // 设备预警等级(0-3)
      isStuckPoint: isLastPointStuck, // 是否为卡钻点
      isMutationPoint: isLastPointMutation, // 是否为突进点（上突进或下突进）
      isUpMutation: isLastPointUpMutation, // 是否为上突进点
      isDownMutation: isLastPointDownMutation, // 是否为下突进点
      mutationType: isLastPointUpMutation ? 'up' : (isLastPointDownMutation ? 'down' : null) // 突进类型：'up'、'down' 或 null
    }]
  } catch (error) {
    console.error('生成实时钻进数据出错:', error)
    return []
  }
}

/**
 * 默认滤波算法 - 数据滤波处理
 * @param {Array} data - 输入数据数组 [{"collectionAt": "2025-04-30T09:31:39.000Z","deviceSn": "PDF-2502-20001","heart": 244, "mode": 0, "strkPct": 20, "18b03": 95, "wrmCd": 0, "dpth": 44360, "rtnTq": 44360, "frcstKn": 44360,"wtrPrsH": 72, "rtnSpd": 2490, "advncSpd": 29, "hydrPrs": 1830,"wtrPrsL": 0, "hghWrk": 0, "lwWrk": 44359, "rtnPrs": 140, "frcstPrs": 710, "clctType": 1, "clctSts": 1, "tunnelDpth": 24, "hlNum": 23, "hlAng": null, "rc0": 127, "rc1": 0, "rc2": 0, "rc3": 0, "rc4": 0, "rc5": null, "rc6": null "led_08": 8, "ledAf": 16, "tunnelName": "abc", "diameter": 90}]
 * @returns {Array} 滤波的数据
 */
function generateDrillingCurveFilteringData(data) {
  try {
    if (!Array.isArray(data) || data.length === 0) {
      console.warn('数据清洗: 输入数据为空或格式错误')
      return data
    }

    console.log(`数据清洗开始: 输入数据量 ${data.length} 条`)

    // 需要进行滤波的字段
    const filterFields = ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn']

    // 深拷贝数据，避免修改原始数据
    let cleanedData = JSON.parse(JSON.stringify(data))

    // 对每个字段进行滤波处理
    filterFields.forEach(field => {
      cleanedData = applyDataFilter(cleanedData, field)
    })

    console.log(`数据清洗完成: 输出数据量 ${cleanedData.length} 条`)
    return cleanedData
  } catch (error) {
    console.error('数据清洗过程中发生错误:', error)
    return data // 出错时返回原始数据
  }
}

/**
 * 对指定字段应用数据滤波
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @returns {Array} 滤波后的数据
 */
function applyDataFilter(data, field) {
  try {
    // 1. 异常值检测和处理
    data = removeOutliers(data, field)

    // 2. 移动平均滤波
    data = movingAverageFilter(data, field, 5) // 5点移动平均

    // 3. 中值滤波
    data = medianFilter(data, field, 3) // 3点中值滤波

    return data
  } catch (error) {
    console.error(`字段 ${field} 滤波处理失败:`, error)
    return data
  }
}

/**
 * 异常值检测和处理 - 使用四分位数方法(IQR)
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @returns {Array} 处理后的数据
 */
function removeOutliers(data, field) {
  try {
    // 提取有效数值
    const values = data
      .map(item => Number(item[field]))
      .filter(val => !isNaN(val) && isFinite(val))

    if (values.length < 4) {
      return data // 数据量太少，不进行异常值处理
    }

    // 计算四分位数
    const sortedValues = [...values].sort((a, b) => a - b)
    const q1Index = Math.floor(sortedValues.length * 0.25)
    const q3Index = Math.floor(sortedValues.length * 0.75)
    const q1 = sortedValues[q1Index]
    const q3 = sortedValues[q3Index]
    const iqr = q3 - q1

    // 计算异常值边界
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr

    console.log(`字段 ${field} 异常值边界: [${lowerBound.toFixed(2)}, ${upperBound.toFixed(2)}]`)

    // 处理异常值
    let outlierCount = 0
    const processedData = data.map((item, index) => {
      const value = Number(item[field])

      if (isNaN(value) || !isFinite(value)) {
        return item // 保持非数值数据不变
      }

      // 检测异常值
      if (value < lowerBound || value > upperBound) {
        outlierCount++

        // 使用邻近值的平均值替换异常值
        const neighborValues = getNeighborValues(data, index, field, 2)
        const replacementValue = neighborValues.length > 0
          ? neighborValues.reduce((sum, val) => sum + val, 0) / neighborValues.length
          : q1 + (q3 - q1) / 2 // 如果没有邻近值，使用中位数

        return {
          ...item,
          [field]: Math.round(replacementValue * 100) / 100 // 保留2位小数
        }
      }

      return item
    })

    if (outlierCount > 0) {
      console.log(`字段 ${field} 检测到 ${outlierCount} 个异常值并已处理`)
    }

    return processedData
  } catch (error) {
    console.error(`字段 ${field} 异常值处理失败:`, error)
    return data
  }
}

/**
 * 获取指定索引周围的邻近有效值
 * @param {Array} data - 数据数组
 * @param {number} index - 当前索引
 * @param {string} field - 字段名
 * @param {number} range - 搜索范围
 * @returns {Array} 邻近有效值数组
 */
function getNeighborValues(data, index, field, range = 2) {
  const neighbors = []

  // 向前搜索
  for (let i = Math.max(0, index - range); i < index; i++) {
    const value = Number(data[i][field])
    if (!isNaN(value) && isFinite(value)) {
      neighbors.push(value)
    }
  }

  // 向后搜索
  for (let i = index + 1; i <= Math.min(data.length - 1, index + range); i++) {
    const value = Number(data[i][field])
    if (!isNaN(value) && isFinite(value)) {
      neighbors.push(value)
    }
  }

  return neighbors
}

/**
 * 移动平均滤波
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {number} windowSize - 窗口大小
 * @returns {Array} 滤波后的数据
 */
function movingAverageFilter(data, field, windowSize = 5) {
  try {
    if (data.length < windowSize) {
      return data // 数据量不足，不进行滤波
    }

    const filteredData = [...data]
    const halfWindow = Math.floor(windowSize / 2)

    for (let i = halfWindow; i < data.length - halfWindow; i++) {
      const windowValues = []

      // 收集窗口内的有效值
      for (let j = i - halfWindow; j <= i + halfWindow; j++) {
        const value = Number(data[j][field])
        if (!isNaN(value) && isFinite(value)) {
          windowValues.push(value)
        }
      }

      // 计算平均值
      if (windowValues.length > 0) {
        const average = windowValues.reduce((sum, val) => sum + val, 0) / windowValues.length
        filteredData[i] = {
          ...filteredData[i],
          [field]: Math.round(average * 100) / 100 // 保留2位小数
        }
      }
    }

    console.log(`字段 ${field} 移动平均滤波完成 (窗口大小: ${windowSize})`)
    return filteredData
  } catch (error) {
    console.error(`字段 ${field} 移动平均滤波失败:`, error)
    return data
  }
}

/**
 * 中值滤波
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {number} windowSize - 窗口大小
 * @returns {Array} 滤波后的数据
 */
function medianFilter(data, field, windowSize = 3) {
  try {
    if (data.length < windowSize) {
      return data // 数据量不足，不进行滤波
    }

    const filteredData = [...data]
    const halfWindow = Math.floor(windowSize / 2)

    for (let i = halfWindow; i < data.length - halfWindow; i++) {
      const windowValues = []

      // 收集窗口内的有效值
      for (let j = i - halfWindow; j <= i + halfWindow; j++) {
        const value = Number(data[j][field])
        if (!isNaN(value) && isFinite(value)) {
          windowValues.push(value)
        }
      }

      // 计算中值
      if (windowValues.length > 0) {
        windowValues.sort((a, b) => a - b)
        const median = windowValues.length % 2 === 0
          ? (windowValues[windowValues.length / 2 - 1] + windowValues[windowValues.length / 2]) / 2
          : windowValues[Math.floor(windowValues.length / 2)]

        filteredData[i] = {
          ...filteredData[i],
          [field]: Math.round(median * 100) / 100 // 保留2位小数
        }
      }
    }

    console.log(`字段 ${field} 中值滤波完成 (窗口大小: ${windowSize})`)
    return filteredData
  } catch (error) {
    console.error(`字段 ${field} 中值滤波失败:`, error)
    return data
  }
}

/**
 * 高级数据清洗函数 - 提供更多配置选项
 * @param {Array} data - 输入数据数组
 * @param {Object} options - 配置选项
 * @returns {Array} 清洗后的数据
 */
function advancedClean(data, options = {}) {
  try {
    const defaultOptions = {
      // 需要滤波的字段
      filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
      // 异常值检测配置
      outlierDetection: {
        enabled: true,
        method: 'iqr', // 'iqr' 或 'zscore'
        threshold: 1.5  // IQR倍数或Z-score阈值
      },
      // 移动平均滤波配置
      movingAverage: {
        enabled: true,
        windowSize: 5
      },
      // 中值滤波配置
      medianFilter: {
        enabled: true,
        windowSize: 3
      },
      // 卡尔曼滤波配置
      kalmanFilter: {
        enabled: false,
        processNoise: 0.1,
        measurementNoise: 0.1
      }
    }

    const config = { ...defaultOptions, ...options }

    if (!Array.isArray(data) || data.length === 0) {
      console.warn('高级数据清洗: 输入数据为空或格式错误')
      return data
    }

    console.log(`高级数据清洗开始: 输入数据量 ${data.length} 条`)
    console.log('清洗配置:', config)

    let cleanedData = JSON.parse(JSON.stringify(data))

    // 对每个字段进行滤波处理
    config.filterFields.forEach(field => {
      console.log(`开始处理字段: ${field}`)

      // 1. 异常值检测和处理
      if (config.outlierDetection.enabled) {
        if (config.outlierDetection.method === 'zscore') {
          cleanedData = removeOutliersZScore(cleanedData, field, config.outlierDetection.threshold)
        } else {
          cleanedData = removeOutliers(cleanedData, field)
        }
      }

      // 2. 移动平均滤波
      if (config.movingAverage.enabled) {
        cleanedData = movingAverageFilter(cleanedData, field, config.movingAverage.windowSize)
      }

      // 3. 中值滤波
      if (config.medianFilter.enabled) {
        cleanedData = medianFilter(cleanedData, field, config.medianFilter.windowSize)
      }

      // 4. 卡尔曼滤波 (可选)
      if (config.kalmanFilter.enabled) {
        cleanedData = kalmanFilter(cleanedData, field, config.kalmanFilter)
      }
    })

    console.log(`高级数据清洗完成: 输出数据量 ${cleanedData.length} 条`)
    return cleanedData

  } catch (error) {
    console.error('高级数据清洗过程中发生错误:', error)
    return data
  }
}

/**
 * 基于Z-score的异常值检测和处理
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {number} threshold - Z-score阈值
 * @returns {Array} 处理后的数据
 */
function removeOutliersZScore(data, field, threshold = 2.5) {
  try {
    // 提取有效数值
    const values = data
      .map(item => Number(item[field]))
      .filter(val => !isNaN(val) && isFinite(val))

    if (values.length < 3) {
      return data
    }

    // 计算均值和标准差
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    const stdDev = Math.sqrt(variance)

    console.log(`字段 ${field} Z-score统计: 均值=${mean.toFixed(2)}, 标准差=${stdDev.toFixed(2)}`)

    // 处理异常值
    let outlierCount = 0
    const processedData = data.map((item, index) => {
      const value = Number(item[field])

      if (isNaN(value) || !isFinite(value)) {
        return item
      }

      // 计算Z-score
      const zScore = Math.abs((value - mean) / stdDev)

      if (zScore > threshold) {
        outlierCount++

        // 使用邻近值的平均值替换异常值
        const neighborValues = getNeighborValues(data, index, field, 2)
        const replacementValue = neighborValues.length > 0
          ? neighborValues.reduce((sum, val) => sum + val, 0) / neighborValues.length
          : mean

        return {
          ...item,
          [field]: Math.round(replacementValue * 100) / 100
        }
      }

      return item
    })

    if (outlierCount > 0) {
      console.log(`字段 ${field} Z-score方法检测到 ${outlierCount} 个异常值并已处理`)
    }

    return processedData
  } catch (error) {
    console.error(`字段 ${field} Z-score异常值处理失败:`, error)
    return data
  }
}

/**
 * 简单卡尔曼滤波实现
 * @param {Array} data - 数据数组
 * @param {string} field - 字段名
 * @param {Object} config - 卡尔曼滤波配置
 * @returns {Array} 滤波后的数据
 */
function kalmanFilter(data, field, config = {}) {
  try {
    const { processNoise = 0.1, measurementNoise = 0.1 } = config

    const filteredData = [...data]
    let estimate = Number(data[0][field]) || 0
    let errorCovariance = 1.0

    for (let i = 0; i < data.length; i++) {
      const measurement = Number(data[i][field])

      if (isNaN(measurement) || !isFinite(measurement)) {
        continue
      }

      // 预测步骤
      const predictedEstimate = estimate
      const predictedErrorCovariance = errorCovariance + processNoise

      // 更新步骤
      const kalmanGain = predictedErrorCovariance / (predictedErrorCovariance + measurementNoise)
      estimate = predictedEstimate + kalmanGain * (measurement - predictedEstimate)
      errorCovariance = (1 - kalmanGain) * predictedErrorCovariance

      filteredData[i] = {
        ...filteredData[i],
        [field]: Math.round(estimate * 100) / 100
      }
    }

    console.log(`字段 ${field} 卡尔曼滤波完成`)
    return filteredData
  } catch (error) {
    console.error(`字段 ${field} 卡尔曼滤波失败:`, error)
    return data
  }
}

/**
 * 数据质量评估
 * @param {Array} originalData - 原始数据
 * @param {Array} cleanedData - 清洗后数据
 * @param {Array} fields - 评估字段
 * @returns {Object} 质量评估报告
 */
function assessDataQuality(originalData, cleanedData, fields = ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn']) {
  try {
    const report = {
      totalRecords: originalData.length,
      processedRecords: cleanedData.length,
      fieldReports: {}
    }

    fields.forEach(field => {
      const originalValues = originalData
        .map(item => Number(item[field]))
        .filter(val => !isNaN(val) && isFinite(val))

      const cleanedValues = cleanedData
        .map(item => Number(item[field]))
        .filter(val => !isNaN(val) && isFinite(val))

      if (originalValues.length === 0) {
        report.fieldReports[field] = { error: '无有效数据' }
        return
      }

      // 计算统计指标
      const originalMean = originalValues.reduce((sum, val) => sum + val, 0) / originalValues.length
      const cleanedMean = cleanedValues.reduce((sum, val) => sum + val, 0) / cleanedValues.length

      const originalStd = Math.sqrt(
        originalValues.reduce((sum, val) => sum + Math.pow(val - originalMean, 2), 0) / originalValues.length
      )
      const cleanedStd = Math.sqrt(
        cleanedValues.reduce((sum, val) => sum + Math.pow(val - cleanedMean, 2), 0) / cleanedValues.length
      )

      report.fieldReports[field] = {
        originalMean: Math.round(originalMean * 100) / 100,
        cleanedMean: Math.round(cleanedMean * 100) / 100,
        originalStd: Math.round(originalStd * 100) / 100,
        cleanedStd: Math.round(cleanedStd * 100) / 100,
        noiseReduction: Math.round(((originalStd - cleanedStd) / originalStd) * 100 * 100) / 100,
        dataIntegrity: Math.round((cleanedValues.length / originalValues.length) * 100 * 100) / 100
      }
    })

    return report
  } catch (error) {
    console.error('数据质量评估失败:', error)
    return { error: error.message }
  }
}

/**
 * 导出清洗配置预设
 */
const FILTER_PRESETS = {
  // 轻度清洗 - 适用于数据质量较好的场景
  light: {
    filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
    outlierDetection: { enabled: true, method: 'iqr', threshold: 2.0 },
    movingAverage: { enabled: false },
    medianFilter: { enabled: true, windowSize: 3 },
    kalmanFilter: { enabled: false }
  },

  // 标准清洗 - 默认推荐配置
  standard: {
    filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
    outlierDetection: { enabled: true, method: 'iqr', threshold: 1.5 },
    movingAverage: { enabled: true, windowSize: 5 },
    medianFilter: { enabled: true, windowSize: 3 },
    kalmanFilter: { enabled: false }
  },

  // 强力清洗 - 适用于噪声较大的数据
  aggressive: {
    filterFields: ['advncSpd', 'rtnSpd', 'rtnTq', 'wtrPrsH', 'frcstKn'],
    outlierDetection: { enabled: true, method: 'zscore', threshold: 2.0 },
    movingAverage: { enabled: true, windowSize: 7 },
    medianFilter: { enabled: true, windowSize: 5 },
    kalmanFilter: { enabled: true, processNoise: 0.1, measurementNoise: 0.1 }
  }
}


