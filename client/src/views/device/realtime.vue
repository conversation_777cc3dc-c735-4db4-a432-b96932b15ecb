<template>
  <div class="device-realtime-container">
    <!-- 头部 -->
    <el-page-header @back="goBack">
      <template #content>
        <div class="header-content">
          <span class="device-name">{{ deviceDetail?.deviceName || '设备' }}</span>
          <span class="device-title text-primary">实时数据</span>
        </div>
      </template>
    </el-page-header>

    <!-- 使用v-loading指令展示加载状态 -->
    <div
      v-loading="loading" 
      element-loading-text="获取设备数据中..." 
      element-loading-background="rgba(0, 0, 0, 0.1)"
      style="width: 100%; min-height: 200px;"
    >
      <!-- 连接状态和控制 -->
      <el-card class="status-card">
        <div class="status-header">
          <div class="connection-status">
            <el-tag
              :type="mqttConnected ? 'success' : 'danger'"
              effect="dark"
              size="large"
              class="status-tag"
            >
              <i
                v-if="mqttConnected"
                class="el-icon-connection"
              />
              <i
                v-else
                class="el-icon-warning"
              />
              {{ mqttConnected ? 'MQTT已连接' : 'MQTT未连接' }}
            </el-tag>
            <div
              v-if="mqttConnected"
              class="topic-container"
            >
              <span class="topic-label">订阅主题:</span>
              <el-tag
                type="primary"
                effect="light"
              >
                {{ subscribedTopic }}
              </el-tag>
            </div>
          </div>
          <div class="control-btns">
            <el-button
              type="primary"
              :disabled="mqttConnected"
              :loading="connecting"
              size="large"
              class="action-button"
              @click="connectMqtt"
            >
              连接
            </el-button>
            <el-button
              type="danger"
              :disabled="!mqttConnected"
              size="large"
              class="action-button"
              @click="disconnectMqtt"
            >
              断开
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 新增的DrillMonitorPanel组件 -->
      <el-card
        class="realtime-view-card"
        style="margin-bottom: 16px; overflow: visible; background-color: #21272D; border: none; max-height: 700px;"
      >
        <div style="width: 100%; height: 700px; overflow: visible; position: relative;">
          <DrillMonitorPanel 
            :telemetry-data="latestTelemetryData" 
            :use-test-data="!latestTelemetryData && mqttConnected"
          />
        </div>
      </el-card>

      <!-- 简化钻进曲线图表组件 -->
      <el-card
        class="chart-card"
        style="margin-bottom: 16px;"
      >
        <MultiGridDrillChart :latest-data="latestTelemetryData" />
      </el-card>

      <!-- 实时数据展示 -->
      <el-card
        class="data-card"
        style="flex: 1; display: flex; flex-direction: column; min-height: 550px;"
      >
        <template #header>
          <div class="card-header">
            <span class="header-title">实时数据</span>
            <div class="header-actions">
              <el-button
                type="primary"
                plain
                size="small"
                :disabled="messages.length === 0"
                class="clear-button"
                @click="clearMessages"
              >
                清空消息
              </el-button>
            </div>
          </div>
        </template>
        <div
          v-if="messages.length === 0"
          style="
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
              background-color: #f8faff;
              border-radius: 8px;
            "
        >
          <el-empty description="暂无数据" />
        </div>
        <div
          v-else
          style="margin: 0; padding: 0; flex: 1; position: relative; width: 100%; overflow: hidden; min-height: 480px;"
        >
          <div
            style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                overflow-x: auto;
                overflow-y: auto;
                max-width: 100%;
                height: 100%;
              "
          >
            <div style="width: 1200px; max-width: 1200px">
              <table style="width: 100%; table-layout: fixed; border-collapse: collapse">
                <colgroup>
                  <col
                    v-for="header in tableHeaders"
                    :key="'col-' + header"
                    :style="{
                      width:
                        header === 'Collection_At' || header === 'Tunnel_Name'
                          ? '200px'
                          : ['Heart', 'Mode', 'Strk_Pct', '18B03', 'Wrn_Cd', 'Dpth'].includes(header)
                            ? '100px'
                            : '120px'
                    }"
                  >
                </colgroup>
                <thead>
                  <tr>
                    <th
                      v-for="header in tableHeaders"
                      :key="header"
                      style="
                          position: sticky;
                          top: 0;
                          z-index: 10;
                          padding: 14px 12px;
                          text-align: center;
                          white-space: normal;
                          overflow: hidden;
                          background-color: #f0f6ff;
                          color: #2c3e50;
                          font-weight: 600;
                          border-bottom: 2px solid #c0d6ff;
                          border-right: 1px solid #e0eaff;
                          line-height: 1.4;
                          min-height: 60px;
                          vertical-align: middle;
                        "
                    >
                      <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 40px;">
                        <div style="font-weight: 600; margin-bottom: 2px;">
                          {{ fieldMappings[header]?.name || header }}
                        </div>
                        <div
                          v-if="fieldMappings[header]?.description"
                          style="font-size: 11px; color: #666; font-weight: 400; line-height: 1.3; word-break: break-word; max-width: 100%;"
                        >
                          {{ fieldMappings[header].description }}
                        </div>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="(row, rowIndex) in formatMessages"
                    :key="rowIndex"
                    style="transition: background-color 0.2s ease; height: 46px;"
                    :style="{ backgroundColor: rowIndex % 2 === 0 ? '#ffffff' : '#f8faff' }"
                  >
                    <td
                      v-for="header in tableHeaders"
                      :key="header"
                      style="
                          padding: 12px 8px;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          border-bottom: 1px solid #ebeef5;
                          border-right: 1px solid #ebeef5;
                          color: #606266;
                          text-align: center;
                          height: 46px;
                        "
                    >
                      {{ formatCellValue(row[header]) }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import mqtt from 'mqtt'
import { getDeviceDetail, getDeviceArithmetic } from '@/api/device'
import { executeChainedAlgorithmMethods } from '@/api/arithmetic'
import { getFieldMappings } from '@/api/data'
import DrillMonitorPanel, { TelemetryData } from '@/components/DrillMonitorPanel.vue'
import MultiGridDrillChart from '@/components/dashboard/MultiGridDrillChart.vue'
import { throttledMqtt } from '@/utils/debounce'

// 工具函数：下划线命名转驼峰命名
const snakeToCamel = (obj: Record<string, any>): Record<string, any> => {
  const result: Record<string, any> = {}
  
  Object.keys(obj).forEach(key => {
    // 将下划线格式转为驼峰格式
    const camelKey = key.replace(/_([a-z])/gi, (_, letter) => letter.toUpperCase())
    // 首字母小写
    const finalKey = camelKey.charAt(0).toLowerCase() + camelKey.slice(1)
    result[finalKey] = obj[key]
  })
  
  return result
}

// 映射存储原始字段名称与格式
const originalFieldFormats: Record<string, string> = {}

// 预处理并存储原始字段格式
const storeOriginalFormat = (obj: Record<string, any>): void => {
  Object.keys(obj).forEach(key => {
    // 保存原始格式的字段名称
    const camelKey = key.replace(/_([a-z])/gi, (_, letter) => letter.toUpperCase())
    const finalKey = camelKey.charAt(0).toLowerCase() + camelKey.slice(1)
    
    originalFieldFormats[finalKey] = key
  })
}

// 工具函数：驼峰命名转回原始格式
const camelToOriginalFormat = (obj: Record<string, any>): Record<string, any> => {
  const result: Record<string, any> = {}
  
  Object.keys(obj).forEach(key => {
    // 使用保存的原始格式或生成一个合理的默认格式
    if (originalFieldFormats[key]) {
      // 使用保存的原始格式
      result[originalFieldFormats[key]] = obj[key]
    } else {
      // 生成一个默认格式（首字母大写并加下划线）
      const snakeKey = key.replace(/([A-Z])/g, '_$1')
      // 首字母大写
      const defaultKey = snakeKey.charAt(0).toUpperCase() + snakeKey.slice(1).toUpperCase()
      result[defaultKey] = obj[key]
    }
  })
  
  return result
}

// MQTT配置项
const mqttConfig = {
  // 服务器连接配置
  server: {
    url: 'ws://8.154.34.3:8083/mqtt', // WebSocket连接地址
    options: {
      clean: true,
      username: 'test',
      password: 'test001',
      connectTimeout: 3000, // 连接超时时间(ms)
      reconnectPeriod: 3000, // 自动重连间隔(ms)
      keepalive: 60, // 心跳间隔(s)
      will: {  // 遗嘱消息，断开连接时服务器会发布此消息
        topic: 'client/status',
        payload: JSON.stringify({ status: 'offline' }),
        qos: 1 as 0 | 1 | 2, // 显式类型转换为MQTT QoS类型
        retain: false
      }
    }
  },
  // 主题配置
  topics: {
    data: (deviceSn: string) => `hub/device/${deviceSn}/data`, // 设备数据主题格式
    control: (deviceSn: string) => `hub/device/${deviceSn}/control` // 控制指令主题格式(预留)
  },
  // 消息配置
  message: {
    maxCount: 100, // 最大消息存储数量
    retainLatest: true // 是否保留最新消息(true为保留最新的,false为保留最早的)
  }
}

const route = useRoute()
const router = useRouter()
const deviceId = route.params.id
const deviceDetail = ref<any>(null)
const deviceSn = ref('')
const mqttClient = ref<any>(null)
const mqttConnected = ref(false)
const connecting = ref(false)
const messages = ref<{ time: string; topic: string; payload: any }[]>([])
const tableHeaders = ref<string[]>([])
const subscribedTopic = ref('')
const deviceArithmeticId = ref<string | null>(null)
const loading = ref(true)
const fieldMappings = ref<Record<string, { name: string; description: string }>>({})
const fieldMappingsLoaded = ref(false)

// 获取字段映射
const fetchFieldMappings = async () => {
  try {
    const response = await getFieldMappings()
    if (response.success && response.data) {
      // 将接口返回的驼峰式字段映射为MQTT使用的下划线格式
      const mappings: Record<string, { name: string; description: string }> = {}

      response.data.forEach((fieldObj: any) => {
        // 每个对象只有一个键值对，键是驼峰式字段名，值是描述
        const camelFieldName = Object.keys(fieldObj)[0]
        const description = fieldObj[camelFieldName]

        // 将驼峰式字段名转换为MQTT使用的下划线格式
        const mqttFieldName = camelToMqttFieldName(camelFieldName)

        // 以MQTT字段名（下划线格式）为key创建映射
        mappings[mqttFieldName] = {
          name: mqttFieldName, // 显示MQTT字段名
          description: description
        }
      })
      console.log('字段映射:', mappings)

      fieldMappings.value = mappings
      fieldMappingsLoaded.value = true
    }
  } catch (error) {
    console.error('获取字段映射失败:', error)
  }
}

// 将驼峰式字段名转换为MQTT使用的下划线格式
const camelToMqttFieldName = (camelName: string): string => {
  // 处理特殊情况的映射
  const specialCases: Record<string, string> = {
    'collectionAt': 'Collection_At',
    'deviceSn': 'Device_Sn',
    'wrmCd': 'Wrn_Cd', // 告警码字段的特殊映射
    'hghWrk': 'Hgh_Wrk',
    'lwWrk': 'Lw_Wrk',
    'tunnelName': 'Tunnel_Name',
    'hlNum': 'HI_Num',
    'hlAng': 'HI_Ang',
    'strkPct': 'Strk_Pct',
    'rtnTq': 'Rtn_Tq',
    'frcstKn': 'Frcst_kN',
    'wtrPrsH': 'Wtr_Prs_H',
    'rtnSpd': 'Rtn_Spd',
    'advncSpd': 'Advnc_Spd',
    'hydrPrs': 'Hydr_Prs',
    'wtrPrsL': 'Wtr_Prs_L',
    'rtnPrs': 'Rtn_Prs',
    'frcstPrs': 'Frcst_Prs',
    'clctType': 'Clct_Type',
    'clctSts': 'Clct_Sts',
    'tunnelDpth': 'Tunnel_Dpth',
    'rc0': 'rc0',
    'rc1': 'rc1',
    'rc2': 'rc2',
    'rc3': 'rc3',
    'rc4': 'rc4',
    'rc5': 'rc5',
    'rc6': 'rc6',
    'led_08': 'LED_08',
    'ledAf': 'LED_AF',
    '18b03': '18B03'
  }

  if (specialCases[camelName]) {
    return specialCases[camelName]
  }

  // 一般情况：驼峰转下划线，每个单词首字母大写
  return camelName.replace(/([A-Z])/g, '_$1').replace(/^_/, '').split('_').map(part =>
    part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
  ).join('_')
}

// 获取设备详情
const fetchDeviceDetail = async () => {
  try {
    loading.value = true
    const response = await getDeviceDetail(deviceId as string)
    deviceDetail.value = response.data
    deviceSn.value = response.data.serialNumber || ''
    
    // 获取设备关联的算法
    try {
      const arithmeticResponse = await getDeviceArithmetic(deviceId as string) as {
        success: boolean;
        data: Array<{
          id: number;
          deviceId: number;
          arithmeticId: number;
          arithmeticType: number;
          arithmeticTypeName: string;
          arithmeticName: string;
          createdAt: string;
          modifiedAt: string;
        }>
      }
      
      if (arithmeticResponse.success && arithmeticResponse.data) {
        if (arithmeticResponse.data.length > 0) {
          // 控件算法类型为1，注意比较条件改为双等号，避免类型问题
          if (arithmeticResponse.data[0].arithmeticType == 1) {
            deviceArithmeticId.value = arithmeticResponse.data[0].arithmeticId.toString()
          } 
        } else {
          console.log('没有找到算法数据')
        }
      } else {
        console.log('API响应不符合预期')
      }
    } catch (arithmeticError) {
      console.error('获取设备算法失败:', arithmeticError)
    }
  } catch (error) {
    console.error('获取设备详情失败:', error)
    ElMessage.error('获取设备详情失败')
  } finally {
    loading.value = false
  }
}

// 连接MQTT
const connectMqtt = () => {
  if (!deviceSn.value) {
    ElMessage.warning('设备SN号不存在，无法连接MQTT')
    return
  }

  connecting.value = true
  // 添加连接提示
  const connectingMsg = ElMessage({
    message: '正在连接到MQTT服务器...',
    type: 'info',
    duration: 0
  })

  // 获取MQTT连接选项
  const options = {
    ...mqttConfig.server.options,
    clientId: `hj_web_${Date.now()}` // 客户端ID使用动态生成的时间戳保证唯一性
  }

  // 尝试连接MQTT服务器
  try {
    mqttClient.value = mqtt.connect(mqttConfig.server.url, options)
  } catch (err) {
    connecting.value = false
    connectingMsg.close()
    ElMessage.error(`MQTT初始化连接错误: ${(err as Error).message || String(err)}`)
    return
  }

  // 连接事件处理
  mqttClient.value.on('connect', () => {
    mqttConnected.value = true
    connecting.value = false
    connectingMsg.close()
    ElMessage.success('MQTT连接成功')

    // 订阅设备数据主题
    const topic = mqttConfig.topics.data(deviceSn.value)
    mqttClient.value.subscribe(topic, (err: any) => {
      if (err) {
        ElMessage.error(`订阅主题失败: ${err.message}`)
        return
      }
      subscribedTopic.value = topic
      ElMessage.success(`已订阅主题: ${topic}`)
    })
  })

  // 接收消息事件
  mqttClient.value.on('message', (topic: string, payload: Buffer) => {
    handleMqttMessage(topic, payload)
  })

  // 错误处理
  mqttClient.value.on('error', (error: any) => {
    connecting.value = false
    const errorMsg = error.message || '未知错误'

    // 提供更具体的错误信息和建议
    if (
      errorMsg.includes('WebSocket') ||
      errorMsg.includes('network') ||
      errorMsg.includes('timeout')
    ) {
      ElMessage.error({
        message: `MQTT连接失败: 无法通过WebSocket连接到服务器。可能的原因：
        1. 服务器未开启WebSocket支持（端口8083/8084）
        2. 服务器防火墙阻止了WebSocket连接
        3. 服务器地址或端口不正确
        请联系系统管理员确认MQTT服务器配置。`,
        duration: 5000
      })
    } else {
      ElMessage.error(`MQTT连接错误: ${errorMsg}`)
    }
  })

  // 重连事件
  mqttClient.value.on('reconnect', () => {
    connecting.value = true
  })

  // 断开连接事件
  mqttClient.value.on('close', () => {
    mqttConnected.value = false
    connecting.value = false
    subscribedTopic.value = ''
  })
}

// 断开MQTT连接
const disconnectMqtt = () => {
  if (mqttClient.value && mqttClient.value.connected) {
    mqttClient.value.end(true, {}, () => {
      mqttConnected.value = false
      subscribedTopic.value = ''
      ElMessage.info('MQTT连接已断开')
    })
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 格式化单元格值展示
const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '-'
  }

  if (typeof value === 'object') {
    return JSON.stringify(value)
  }

  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  return String(value)
}

// 根据收到的消息，动态生成表格表头
const updateTableHeaders = (payload: any) => {
  if (!payload || typeof payload !== 'object') return

  const keys = Object.keys(payload)
  
  // 先处理Collection_At，确保它在数组的第一位
  if (keys.includes('Collection_At')) {
    // 如果tableHeaders中已有Collection_At，先移除它
    const collectionAtIndex = tableHeaders.value.indexOf('Collection_At')
    if (collectionAtIndex > -1) {
      tableHeaders.value.splice(collectionAtIndex, 1)
    }
    
    // 如果tableHeaders为空，直接放入Collection_At
    if (tableHeaders.value.length === 0) {
      tableHeaders.value.push('Collection_At')
    } 
    // 否则确保Collection_At在第一位
    else if (tableHeaders.value[0] !== 'Collection_At') {
      tableHeaders.value.unshift('Collection_At')
    }
  }
  
  // 添加其他列（除了Collection_At）
  keys.forEach(key => {
    if (key !== 'Collection_At' && !tableHeaders.value.includes(key)) {
      tableHeaders.value.push(key)
    }
  })
}

// 格式化后的消息，用于表格展示
const formatMessages = computed(() => {
  // 返回新数组，最新消息在前面
  return messages.value
    .map((msg: any) => {
      const result: Record<string, any> = {}

      // 将payload中的字段填入结果对象
      if (typeof msg.payload === 'object') {
        Object.keys(msg.payload).forEach(key => {
          result[key] = msg.payload[key]
        })
      } else {
        // 如果不是对象，添加一个默认字段
        result.value = msg.payload
      }

      return result
    })
    .reverse() // 反转数组使最新的在前面
})

// 清空消息
const clearMessages = () => {
  messages.value = []
  tableHeaders.value = [] // 清空表头
}

// 添加消息处理函数
const addMessage = (topic: string, payloadData: any) => {
  const now = new Date()
  const timeStr = now.toLocaleTimeString()

  // 更新表头
  if (typeof payloadData === 'object') {
    updateTableHeaders(payloadData)
  }

  // 添加新消息
  messages.value.push({
    time: timeStr,
    topic,
    payload: payloadData
  })

  // 限制消息数量，防止内存占用过多
  if (messages.value.length > mqttConfig.message.maxCount) {
    if (mqttConfig.message.retainLatest) {
      // 保留最新消息，删除旧消息
      messages.value = messages.value.slice(-mqttConfig.message.maxCount)
    } else {
      // 保留最早消息，删除新消息
      messages.value = messages.value.slice(0, mqttConfig.message.maxCount)
    }
  }
}

// 使用非null初始值
const latestTelemetryData = ref<TelemetryData>({});

// 移除图表历史数据维护，改为在组件内部维护

// 防止并发执行的标志
let isProcessingAlgorithm = false

// 创建节流的数据处理函数
const throttledDataProcessor = throttledMqtt(async (topic: string, payloadData: any) => {
  // 如果有算法配置且未在处理中，进行算法处理
  if (deviceArithmeticId.value && !isProcessingAlgorithm) {
    isProcessingAlgorithm = true
    try {
      // 将下划线格式转为驼峰格式
      const camelData = snakeToCamel(payloadData);

      // 调用算法处理
      const result = await executeChainedAlgorithmMethods(
        deviceArithmeticId.value,
        ['dataPrecisionProcessing', 'generateDrillingData'],
        [camelData]
      );

      if (result?.data) {
        // 将结果转回原始格式
        const processedData = Array.isArray(result.data)
          ? result.data.map(item => camelToOriginalFormat(item))
          : camelToOriginalFormat(result.data);

        // 直接用处理后的数据替换原始数据
        if (Array.isArray(processedData) && processedData.length > 0) {
          Object.assign(payloadData, processedData[0]);
        } else if (!Array.isArray(processedData)) {
          Object.assign(payloadData, processedData);
        }

        // 更新为处理后的遥测数据
        latestTelemetryData.value = payloadData;
      }
    } catch (algorithmError) {
      console.error('算法处理数据失败:', algorithmError);
    } finally {
      isProcessingAlgorithm = false
    }
  } else {
    // 没有算法处理的情况，直接更新遥测数据
    latestTelemetryData.value = payloadData;
  }

  // 将数据（原始或处理后的）添加到消息列表
  addMessage(topic, payloadData);
});

// 处理MQTT消息方法
const handleMqttMessage = async (topic: string, payload: Buffer) => {
  try {
    const payloadStr = payload.toString();
    const payloadData = JSON.parse(payloadStr);

    // 存储原始字段格式（不节流）
    storeOriginalFormat(payloadData);

    // 所有处理都通过节流函数进行，包括添加到消息列表
    throttledDataProcessor(topic, payloadData);

  } catch (error) {
    console.error('消息解析错误:', error);
    // 对于无法解析为JSON的消息，仍然添加到消息列表但显示为字符串
    addMessage(topic, payload.toString());
  }
};

onMounted(async () => {
  // 并行获取设备详情和字段映射
  await Promise.all([
    fetchDeviceDetail(),
    fetchFieldMappings()
  ])

  // 获取设备详情后自动连接MQTT
  if (deviceSn.value) {
    connectMqtt()
  }
})

onBeforeUnmount(() => {
  // 断开MQTT连接
  disconnectMqtt()

  // 清理节流函数
  if (throttledDataProcessor && typeof throttledDataProcessor.cancel === 'function') {
    throttledDataProcessor.cancel()
  }
})


</script>

<style scoped>
.device-realtime-container {
  padding: 16px 24px;
  background-color: #f5f7fa;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
  min-height: calc(100vh - 100px);
  height: auto;
  display: flex;
  flex-direction: column;
}

.page-header {
  background-color: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
}

.device-name {
  font-size: 20px;
  font-weight: bold;
  margin-right: 16px;
  color: #303133;
}

.device-title {
  font-size: 14px;
  background-color: rgba(0, 113, 227, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}

/* 卡片统一样式 */
.status-card,
.data-card {
  margin-bottom: 16px;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.status-card:hover,
.data-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 状态卡片 */
.status-card {
  margin-top: 12px;
  margin-bottom: 12px;
  background: #fff;
  position: relative;
  flex-shrink: 0;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.connection-status {
  display: flex;
  align-items: center;
}

.topic-container {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.topic-label {
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
}

.topic-tag {
  font-family: monospace;
  font-size: 13px;
  padding: 0 12px;
  height: 32px;
  line-height: 30px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.status-tag {
  font-size: 13px;
  padding: 6px 12px;
  height: auto;
  border-radius: 6px;
}

.action-button {
  min-width: 90px;
  margin-left: 12px;
  transition: all 0.3s ease;
  font-size: 14px;
  padding: 8px 15px;
  height: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.clear-button {
  color: #ffffff !important;
  font-weight: 600 !important;
  background-color: #409eff !important;
  border-color: #409eff !important;
  padding: 8px 16px;
}

.clear-button:hover {
  color: #ffffff !important;
  background-color: #66b1ff !important;
  border-color: #66b1ff !important;
}

.clear-button[disabled] {
  color: #c0c4cc !important;
  background-color: #ecf5ff !important;
  border-color: #d9ecff !important;
}

/* 无数据状态 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8faff;
  height: 400px;
  border-radius: 8px;
}

/* 表格容器 */
.table-container {
  overflow: hidden;
  margin: 0;
  padding: 0;
}

/* 简化表格包装器 */
.simple-table-wrapper {
  width: 100%;
  height: 400px;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

/* 简化表格样式 */
.simple-table {
  width: 1200px;
  table-layout: fixed;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
}

/* 表头样式 */
.table-header {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 14px 12px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f0f6ff;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #c0d6ff;
  border-right: 1px solid #e0eaff;
  width: 140px;
}

/* 日期列宽度 */
.date-column {
  width: 180px;
}

/* 窄列宽度 */
.narrow-column {
  width: 100px;
}

/* 表格行样式 */
.simple-table tr {
  transition: background-color 0.2s ease;
}

/* 奇偶行样式 */
.simple-table tr:hover {
  background-color: #ecf5ff !important;
}

.even-row {
  background-color: #ffffff;
}

.simple-table tr:not(.even-row) {
  background-color: #f8faff;
}

/* 单元格样式 */
.simple-table td {
  padding: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  color: #606266;
  transition: background-color 0.2s ease;
}

/* 数字单元格对齐右侧 */
.numeric-cell {
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .control-btns {
    margin-top: 16px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .topic-container {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }

  .topic-tag {
    max-width: 200px;
  }
}

/* 数据卡片 - 调整样式 */
.data-card {
  margin-bottom: 16px;
  border-radius: 12px;
  width: 100%;
  box-sizing: border-box;
  min-width: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 18px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.el-card {
  max-width: 100%;
  overflow: hidden !important;
}

:deep(.el-card__body) {
  padding: 15px;
  position: relative;
  overflow: hidden !important;
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 100%;
}

.data-card :deep(.el-card__body) {
  padding: 20px;
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 100%;
  overflow: hidden !important;
}

.realtime-view-card {
  margin-bottom: 16px;
}

.chart-card {
  background: #fff; /* 白色背景，匹配原组件 */
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); /* 匹配原组件阴影 */
  height: 800px; /* 为多网格图表提供足够的高度 */
  overflow: hidden;
  position: relative;
}

.chart-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

/* 添加顶部装饰条，匹配原组件 */
.chart-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  z-index: 1;
}

.chart-card :deep(.el-card__body) {
  padding: 20px;
  height: calc(100% - 0px); /* 没有header，所以减去0px */
}
</style>
